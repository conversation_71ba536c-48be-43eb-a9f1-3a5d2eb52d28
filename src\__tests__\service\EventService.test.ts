import { EventService } from '../../services/EventService';
import { getEdgeDataBySerialNumber } from '../../services/EdgeService';
import { MongoDBAdapter } from '../../adapters/mongodb';
import { EventModel } from '../../schemas/event.schema';
import { TargetFirmwareNames, TargetUpdateOperations } from '../../enum';
import { EventPayload } from '../../models/interfaces/EventPayload';

jest.mock('../../services/EdgeService', () => ({
  getEdgeDataBySerialNumber: jest.fn(),
}));

jest.mock('../../adapters/mongodb', () => ({
  MongoDBAdapter: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(true),
  })),
}));

jest.mock('../../schemas/event.schema', () => ({
  EventModel: {
    create: jest.fn() as jest.Mock,
  },
}));

describe('EventService', () => {
  let eventService: EventService;

  const EDGE_SERIAL_NUMBER = 'EDGE123';
  const USER_NAME = 'Mark James';
  const EVENT_NAME = 'Firmware Job';
  const TARGET_DIRECTORY = TargetUpdateOperations.BulksAll;
  const VERSION = [{ firmwareName: TargetFirmwareNames.ESP32_filesystem, version: '1.0.0' }];
  const EDGE_ID = 'edgeId123';
  const DESCRIPTION = `${USER_NAME} updated ${VERSION[0].firmwareName} to ${VERSION[0].version} for ${EDGE_SERIAL_NUMBER}`;

  beforeEach(() => {
    eventService = new EventService();
    jest.clearAllMocks();
  });

  it('should create a new event successfully', async () => {
    const eventPayload: EventPayload = {
      edgeSerialNumber: EDGE_SERIAL_NUMBER,
      userName: USER_NAME,
      eventName: EVENT_NAME,
      targetUpdateOperation: TARGET_DIRECTORY,
      targetVersions: VERSION,
    };

    const edgeData = [
      {
        id: EDGE_ID,
        serialNumber: EDGE_SERIAL_NUMBER,
      },
    ];

    (getEdgeDataBySerialNumber as jest.Mock).mockResolvedValue(edgeData);
    const createdEvent = {
      timestamp: +new Date(),
      edgeId: EDGE_ID,
      assetSN: '',
      assetName: '',
      assetId: '',
      name: EVENT_NAME,
      description: DESCRIPTION,
      properties: [
        { key: 'targetDirectory', value: TARGET_DIRECTORY },
        { key: 'version', value: VERSION },
      ],
      edgeSN: EDGE_SERIAL_NUMBER,
    };

    (EventModel.create as jest.Mock).mockResolvedValue(createdEvent);

    const result = await eventService.createNewEvent(eventPayload);

    expect(getEdgeDataBySerialNumber).toHaveBeenCalledWith(EDGE_SERIAL_NUMBER);
    expect(MongoDBAdapter).toHaveBeenCalledTimes(1);
    expect(EventModel.create).toHaveBeenCalledWith({
      timestamp: expect.any(Number),
      edgeId: EDGE_ID,
      assetSN: '',
      assetName: '',
      assetId: '',
      name: EVENT_NAME,
      description: DESCRIPTION,
      properties: [
        { key: 'targetDirectory', value: TARGET_DIRECTORY },
        { key: 'targetVersions', value: JSON.stringify(VERSION) },
      ],
      edgeSN: EDGE_SERIAL_NUMBER,
    });

    expect(result).toEqual(createdEvent);
  });
});
