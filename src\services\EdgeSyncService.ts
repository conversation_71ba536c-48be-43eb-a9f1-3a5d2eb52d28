/* eslint-disable no-await-in-loop */
import { BaseService, SelectParams } from '@carrier-io/backend-lib-node-sdk';
import { logger } from '../helpers';
import { EdgeEntity } from '../models/entities/EdgeEntity';
import { MongoDBAdapter } from '../adapters';
import { AssetEntity, CurrentJob, EdgeJobDetails, EdgeSyncConfig, SyncResult } from '../models';
import { EdgeModel } from '../schemas';
import { JobStatus, TargetFirmwareNames,JobExecutionStatus } from '../enum';
import { EdgeListingService } from './EdgeListingService';

export class EdgeSyncService {
  private edgeService: BaseService<EdgeEntity>;

  private edgeListingService: EdgeListingService;

  private mongoDb: MongoDBAdapter;

  private config: EdgeSyncConfig | undefined;

  constructor(config?: EdgeSyncConfig) {
    this.edgeService = new BaseService<EdgeEntity>(EdgeEntity);
    this.edgeListingService = new EdgeListingService();
    this.mongoDb = new MongoDBAdapter();
    // Setting Default value. In future this will be removed ones it moved to CSSEVEN
    this.config = config
  }

  private async init() {
    await this.mongoDb.connect();
  }

  public async syncAllEdgesToMongo(): Promise<SyncResult | null> {
    await this.init();
    if(this.config){
      const result: SyncResult = {
        syncedCount: 0,
        failedCount: 0,
        totalProcessed: 0,
        details: {
          successful: [],
          failed: [],
        },
      };

      try {
        logger.info('Starting comprehensive edge sync to MongoDB', {
          batchSize: this.config?.batchSize,
          includeAssets: this.config?.includeAssets,
        });

        let offset = 0;
        const { batchSize = 50 } = this.config;
        let hasMoreData = true;
        let batchNumber = 1;

        while (hasMoreData) {
          logger.info(`Processing batch ${batchNumber} (offset: ${offset})`);

          const params: SelectParams<EdgeEntity> = {
            filters: {
              ...(this.config.filters || {
                domain: {
                  equalTo: 'edge',
                },
              }),
            },
            slicing: {
              limit: batchSize,
              offset,
            },
            ...(this.config.includeAssets && { include: ['assets'] }),
          };
          // eslint-disable-next-line no-await-in-loop
          const edges = await this.edgeListingService.getEdgesBasedOnModel(offset, batchSize, params);

          if (edges.length === 0) {
            logger.info('No more edges to process');
            hasMoreData = false;
            break;
          }

          logger.info(`Processing ${edges.length} edges in batch ${batchNumber}`);

          // Processing batch with error handling
          // eslint-disable-next-line no-await-in-loop
          const batchResult = await this.processBatch(edges, batchNumber);

          // Aggregating results
          result.syncedCount += batchResult.syncedCount;
          result.failedCount += batchResult.failedCount;
          result.totalProcessed += edges.length;
          result.details.successful.push(...batchResult.details.successful);
          result.details.failed.push(...batchResult.details.failed);

          offset += batchSize;
          batchNumber += 1;
          // If we got fewer records than batch size, we've reached the end
          if (edges.length < batchSize) {
            hasMoreData = false;
          }
          // Adding small delay between batches to avoid overwhelming the system
          // eslint-disable-next-line no-await-in-loop
          await this.delay(100);
        }

        logger.info(`Edge sync completed successfully`, {
          totalProcessed: result.totalProcessed,
          syncedCount: result.syncedCount,
          failedCount: result.failedCount,
          successRate: `${((result.syncedCount / result.totalProcessed) * 100).toFixed(2)}%`,
        });
        return result;
      } catch (error: any) {
        logger.error('Critical error during edge sync process', error);
        throw error;
      } finally {
        await this.mongoDb.closeConnection();
      }
    }
    logger.info('Configs are not provided as partof initialization');
    return null
  }

  private async processBatch(edges: EdgeEntity[], batchNumber: number): Promise<SyncResult> {
    const batchResult: SyncResult = {
      syncedCount: 0,
      failedCount: 0,
      totalProcessed: edges.length,
      details: {
        successful: [],
        failed: [],
      },
    };

    // Processing edges in parallel with concurrency limit
    const concurrencyLimit = 5;
    const chunks = this.chunkArray(edges, concurrencyLimit);

    for (const chunk of chunks) {
      const promises = chunk.map((edge) => this.syncSingleEdgeWithRetry(edge));
      const results = await Promise.allSettled(promises);

      results.forEach((result, index) => {
        const edge = chunk[index];
        const edgeId = edge.edgeId || edge.name || edge.id || 'unknown';

        if (result.status === 'fulfilled') {
          batchResult.syncedCount += 1;
          batchResult.details.successful.push(edgeId);
          logger.debug(`Successfully synced edge: ${edgeId}`);
        } else {
          batchResult.failedCount += 1;
          batchResult.details.failed.push({
            edgeId,
            error: result.reason?.message || 'Unknown error',
          });
          logger.error(`Failed to sync edge: ${edgeId}`, result.reason);
        }
      });
    }

    logger.info(`Batch ${batchNumber} completed`, {
      processed: batchResult.totalProcessed,
      synced: batchResult.syncedCount,
      failed: batchResult.failedCount,
    });

    return batchResult;
  }

  private async syncSingleEdgeWithRetry(edge: EdgeEntity): Promise<void> {
    let lastError: any = '';
    for (let attempt = 1; this.config && attempt <= this.config?.maxRetries; attempt += 1) {
      try {
        await this.syncSingleEdge(edge);
        return; // Success, exit retry loop
      } catch (error: any) {
        lastError = error;
        logger.warn(`Sync attempt ${attempt} failed for edge ${edge.edgeId || edge.id}`, {
          error: error.message,
          attempt,
          maxRetries: this.config?.maxRetries,
        });

        if (this.config && attempt < this.config.maxRetries) {
          // Exponential backoff
          await this.delay(2 ** attempt * 100);
        }
      }
    }

    throw lastError;
  }

  private async syncSingleEdge(edge: any): Promise<void> {
    try {
      const transformedEdge = this.transformEdgeForMongo(edge);

      logger.info(`Transformed Edge::: ${JSON.stringify(transformedEdge)}`);

      let siteName = 'Unknown';
      if (!edge.assets?.length) {
        logger.debug(`No assets found for edge: ${edge.edgeId || 'No id found.'}`);
      } else {
        const firstAsset: AssetEntity = edge.assets[0];
        const assedData: AssetEntity = await this.edgeListingService.getAssetLocationDetailsById(firstAsset.id);
        if (assedData.location?.length) {
          siteName = assedData.location[0].name || 'Unknown';
        }
      }

      await EdgeModel.updateOne(
        { edgeNodeId: transformedEdge.edgeNodeId },
        {
          $set: {
            id: transformedEdge.id,
            siteName,
            edgeId: transformedEdge.edgeId,
            isOnline: transformedEdge.isOnline,
            targets: transformedEdge.targets,
          },
          ...(transformedEdge.$setOnInsert && { $setOnInsert: transformedEdge.$setOnInsert }),
        },
        { upsert: true },
      );

      logger.info(`Successfully synced edge: ${edge.id}`);
    } catch (error: any) {
      logger.error(`Failed to sync edge: ${edge.id}`, { error });
      throw error;
    }
  }

  private transformEdgeForMongo(edgeData: EdgeEntity): any {
    logger.info(`This is edge before transformation::: ${JSON.stringify(edgeData)}`);
    const targets = [];

    targets.push({
      targetName: TargetFirmwareNames.ESP32_firmware,
      version: edgeData.esp32Version || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.RX651_CHL,
      version: edgeData.rx651Version || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.LTEmodule,
      version: edgeData.lteVersion || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.ESP32_filesystem,
      version: '',
    });

    return {
      edgeNodeId: edgeData.id, // UUID from node domain,
      edgeId: edgeData.edgeId || edgeData.name || null, // IMEI as edgeId
      rx651Version: edgeData.rx651Version || null,
      lteVersion: edgeData.lteVersion || null,
      esp32Version: edgeData.esp32Version || null,
      isOnline: edgeData.isOnline || false,
      targets,
      $setOnInsert: {
        currentJob: {
          jobId: null,
          jobStatus: null,
          completedAt: null,
          targets: [],
        },
      },
    };
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  public static async updateEdgeDataByEdgeId(edgeId: string, edgeJobDetails: EdgeJobDetails): Promise<any> {
    try {
      // Fix: Use JSON.stringify to properly log the object
      logger.info(`Updating edge job details: ${JSON.stringify(edgeJobDetails)}`);

      // Validate required fields
      if (!edgeJobDetails.currentJob) {
        throw new Error('ongoingJobExecution is required');
      }

      const { jobId, targets, jobStatus } = edgeJobDetails.currentJob;

      // Validate jobId - should not be null or empty
      if (!jobId || jobId.trim() === '') {
        throw new Error('jobId is required and cannot be null or empty');
      }

      // Validate targets array - should not be empty
      if (!targets || !Array.isArray(targets) || targets.length === 0) {
        throw new Error('targets array is required and cannot be empty');
      }

      if (jobStatus && !Object.values(JobStatus).includes(jobStatus as JobStatus)) {
        const validStatuses = Object.values(JobStatus).join(', ');
        throw new Error(`Invalid jobStatus. Must be one of: ${validStatuses}`);
      }

      // Find and update the edge by edgeId using the provided edge job details
      const updatedEdge = await EdgeModel.findOneAndUpdate(
        { edgeId }, // Filter by edgeId
        {
          $set: {
            ongoingJobExecution: {
              ...edgeJobDetails.currentJob,
            },
          },
        },
        {
          new: true,
        },
      );

      if (!updatedEdge) {
        logger.warn(`No edge found with edgeId: ${edgeId}`);
        return null;
      }

      logger.info(`Successfully updated edge details: ${JSON.stringify(updatedEdge.toObject())}`);
      return updatedEdge;
    } catch (error) {
      logger.error(`Error updating edge job details for edgeId ${edgeId}:`, { error });
      throw error;
    }
  }

  public static async checkActiveJob(thingId: string): Promise<boolean> {
    try {
      // Check in your database for any active job for this thing
      const activeJobCount = await EdgeModel.countDocuments({
        edgeId: thingId,
        'currentJob.jobStatus': {
          $in: [JobStatus.Queued, JobStatus.InProgress],
        },
      });

      return activeJobCount > 0;
    } catch (error) {
      logger.error(`Error checking active jobs for thing ${thingId}:`, { error });
      // Return true to be safe - prevent job creation if we can't verify
      return true;
    }
  }


 
  public  async updateJobStatusAndVersion (edgeId : string,currentJobDetails: CurrentJob) : Promise<any>{
       try{
        await this.init()
        const jobStatus = JobExecutionStatus[currentJobDetails.jobStatus as keyof typeof JobExecutionStatus] ?? null;
        logger.info("Curent job details", `${JSON.stringify(currentJobDetails)}`)
         const updateEdgeJobStatus =  await EdgeModel.findOneAndUpdate({
            edgeId
         },
        {
           $set : {
                currentJob : {
                  jobId : currentJobDetails.jobId,
                  jobStatus : jobStatus,
                  completedAt : currentJobDetails.completedAt,
                  targets : currentJobDetails.targets
                }
           },
           updatedAt : new Date()
        })
        if(!updateEdgeJobStatus){
          logger.warn(`No edge found with edgeId: ${edgeId}`);
          return null;
        }
      }
       catch(error){
        logger.error(`Error while updating the job status and target version ${edgeId}`, { error });
       }
  }
}
