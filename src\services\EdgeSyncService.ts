/* eslint-disable no-await-in-loop */
import { BaseService, SelectParams } from '@carrier-io/backend-lib-node-sdk';
import { logger } from '../helpers';
import { EdgeEntity } from '../models/entities/EdgeEntity';
import { MongoDBAdapter } from '../adapters';
import { AssetEntity, CurrentJob, EdgeJobDetails, EdgeSyncConfig, SyncResult, Targets } from '../models';
import { EdgeModel } from '../schemas';
import { JobStatus, TargetFirmwareNames, JobExecutionStatus } from '../enum';
import { EdgeListingService } from './EdgeListingService';

export class EdgeSyncService {
  private edgeService: BaseService<EdgeEntity>;

  private edgeListingService: EdgeListingService;

  private mongoDb: MongoDBAdapter;

  private config: EdgeSyncConfig | undefined;

  constructor(config?: EdgeSyncConfig) {
    this.edgeService = new BaseService<EdgeEntity>(EdgeEntity);
    this.edgeListingService = new EdgeListingService();
    this.mongoDb = new MongoDBAdapter();
    // Setting Default value. In future this will be removed ones it moved to CSSEVEN
    this.config = config;
  }

  private async init() {
    await this.mongoDb.connect();
  }

  public async syncAllEdgesToMongo(): Promise<SyncResult | null> {
    await this.init();

    if (!this.config) {
      logger.debug('Configs are not provided as part of initialization');
      return null;
    }

    const initialResult = this.initializeSyncResult();

    try {
      this.logSyncStart();
      const finalResult = await this.processBatchesInLoop(initialResult);
      this.logSyncCompletion(finalResult);
      return finalResult;
    } catch (error: any) {
      logger.error('Critical error during edge sync process', error);
      throw error;
    }
  }

  private initializeSyncResult(): SyncResult {
    return {
      syncedCount: 0,
      failedCount: 0,
      totalProcessed: 0,
      details: {
        successful: [],
        failed: [],
      },
    };
  }

  private logSyncStart(): void {
    if (!this.config) return;

    logger.debug('Starting comprehensive edge sync to MongoDB', {
      batchSize: this.config.batchSize,
      includeAssets: this.config.includeAssets,
    });
  }

  private async processBatchesInLoop(initialResult: SyncResult): Promise<SyncResult> {
    if (!this.config) {
      throw new Error('Configuration is required for batch processing');
    }

    let offset = 0;
    const { batchSize = 50 } = this.config;
    let batchNumber = 1;
    let currentResult = initialResult; // ← Local variable to accumulate
    const hasMoreEdges = true;

    while (hasMoreEdges) {
      logger.debug(`Processing batch ${batchNumber} (offset: ${offset})`);

      const params = this.buildSelectParams(offset, batchSize);
      const allEdges = await this.edgeListingService.getEdgesBasedOnModel(offset, batchSize, params);

      if (!allEdges.length) {
        logger.debug('No more edges to process');
        break;
      }

      currentResult = await this.processSingleBatch(allEdges, batchNumber, currentResult);

      offset += batchSize;
      batchNumber += 1;
      await this.delay(100);
    }

    return currentResult;
  }

  private buildSelectParams(offset: number, batchSize: number): SelectParams<EdgeEntity> {
    if (!this.config) {
      throw new Error('Configuration is required for building select parameters');
    }

    return {
      filters: {
        ...(this.config.filters || {
          domain: {
            equalTo: 'edge',
          },
        }),
      },
      slicing: {
        limit: batchSize,
        offset,
      },
      ...(this.config.includeAssets && { include: ['assets'] }),
    };
  }

  private async processSingleBatch(
    allEdges: EdgeEntity[],
    batchNumber: number,
    result: SyncResult,
  ): Promise<SyncResult> {
    const edges = this.filterValidEdges(allEdges);
    logger.info(`Batch ${batchNumber}: Found ${allEdges.length} edges, ${edges.length} valid after filtering`);

    if (edges.length === 0) {
      logger.debug(`Batch ${batchNumber} had no valid edges after filtering`);
      return result;
    }

    logger.debug(`Processing ${edges.length} edges in batch ${batchNumber}`);
    const batchResult = await this.processBatch(edges, batchNumber);

    // Create a new result object instead of modifying the parameter
    const updatedResult: SyncResult = {
      syncedCount: result.syncedCount + batchResult.syncedCount,
      failedCount: result.failedCount + batchResult.failedCount,
      totalProcessed: result.totalProcessed + edges.length,
      details: {
        successful: [...result.details.successful, ...batchResult.details.successful],
        failed: [...result.details.failed, ...batchResult.details.failed],
      },
    };

    return updatedResult;
  }

  private filterValidEdges(allEdges: EdgeEntity[]): EdgeEntity[] {
    return allEdges.filter((edge) => edge.isDeleted !== true && edge.isEdgeReplaced !== true);
  }

  private logSyncCompletion(result: SyncResult): void {
    logger.debug(`Edge sync completed successfully`, {
      totalProcessed: result.totalProcessed,
      syncedCount: result.syncedCount,
      failedCount: result.failedCount,
      successRate:
        result.totalProcessed > 0 ? `${((result.syncedCount / result.totalProcessed) * 100).toFixed(2)}%` : '0%',
    });
  }

  private async processBatch(edges: EdgeEntity[], batchNumber: number): Promise<SyncResult> {
    const batchResult: SyncResult = {
      syncedCount: 0,
      failedCount: 0,
      totalProcessed: edges.length,
      details: {
        successful: [],
        failed: [],
      },
    };

    // Processing edges in parallel with concurrency limit
    const concurrencyLimit = 5;
    const chunks = this.chunkArray(edges, concurrencyLimit);

    for (const chunk of chunks) {
      const promises = chunk.map((edge) => this.syncSingleEdgeWithRetry(edge));
      const results = await Promise.allSettled(promises);

      results.forEach((result, index) => {
        const edge = chunk[index];
        const edgeId = edge.edgeId || edge.name || edge.id || 'unknown';

        if (result.status === 'fulfilled') {
          batchResult.syncedCount += 1;
          batchResult.details.successful.push(edgeId);
          logger.debug(`Successfully synced edge: ${edgeId}`);
        } else {
          batchResult.failedCount += 1;
          batchResult.details.failed.push({
            edgeId,
            error: result.reason?.message || 'Unknown error',
          });
          logger.error(`Failed to sync edge: ${edgeId}`, result.reason);
        }
      });
    }

    logger.debug(`Batch ${batchNumber} completed`, {
      processed: batchResult.totalProcessed,
      synced: batchResult.syncedCount,
      failed: batchResult.failedCount,
    });

    return batchResult;
  }

  private async syncSingleEdgeWithRetry(edge: EdgeEntity): Promise<void> {
    let lastError: any = '';
    for (let attempt = 1; this.config && attempt <= this.config?.maxRetries; attempt += 1) {
      try {
        await this.syncSingleEdge(edge);
        return; // Success, exit retry loop
      } catch (error: any) {
        lastError = error;
        logger.warn(`Sync attempt ${attempt} failed for edge ${edge.edgeId || edge.id}`, {
          error: error.message,
          attempt,
          maxRetries: this.config?.maxRetries,
        });

        if (this.config && attempt < this.config.maxRetries) {
          // Exponential backoff
          await this.delay(2 ** attempt * 100);
        }
      }
    }

    throw lastError;
  }

  private async syncSingleEdge(edge: any): Promise<void> {
    try {
      const transformedEdge = this.transformEdgeForMongo(edge);

      logger.debug(`Transformed Edge::: ${JSON.stringify(transformedEdge)}`);

      let siteName = 'Unknown';
      if (!edge.assets?.length) {
        logger.debug(`No assets found for edge: ${edge.edgeId || 'No id found.'}`);
      } else {
        const firstAsset: AssetEntity = edge.assets[0];
        const assedData: AssetEntity = await this.edgeListingService.getAssetLocationDetailsById(firstAsset.id);
        if (assedData.location?.length) {
          siteName = assedData.location[0].name || 'Unknown';
        }
      }

      await EdgeModel.updateOne(
        { edgeNodeId: transformedEdge.edgeNodeId },
        {
          $set: {
            id: transformedEdge.id,
            siteName,
            edgeId: transformedEdge.edgeId,
            isOnline: transformedEdge.isOnline,
            targets: transformedEdge.targets,
          },
          ...(transformedEdge.$setOnInsert && { $setOnInsert: transformedEdge.$setOnInsert }),
        },
        { upsert: true },
      );

      logger.debug(`Successfully synced edge: ${edge.id}`);
    } catch (error: any) {
      logger.error(`Failed to sync edge: ${edge.id}`, { error });
      throw error;
    }
  }

  private transformEdgeForMongo(edgeData: EdgeEntity): any {
    logger.debug(`This is edge before transformation::: ${JSON.stringify(edgeData)}`);
    const targets = [];

    targets.push({
      targetName: TargetFirmwareNames.ESP32_firmware,
      version: edgeData.esp32Version || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.RX651_CHL,
      version: edgeData.rx651Version || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.LTEmodule,
      version: edgeData.lteVersion || '',
    });

    targets.push({
      targetName: TargetFirmwareNames.ESP32_filesystem,
      version: '',
    });

    return {
      edgeNodeId: edgeData.id,
      edgeId: edgeData.edgeId || edgeData.name || null,
      rx651Version: edgeData.rx651Version || null,
      lteVersion: edgeData.lteVersion || null,
      esp32Version: edgeData.esp32Version || null,
      isOnline: edgeData.isOnline || false,
      targets,
      $setOnInsert: {
        currentJob: {
          jobId: null,
          jobStatus: null,
          completedAt: null,
          targets: [],
        },
      },
    };
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(resolve, ms);
    });
  }

  public static async updateEdgeDataByEdgeId(edgeId: string, edgeJobDetails: EdgeJobDetails): Promise<any> {
    try {
      // Fix: Use JSON.stringify to properly log the object
      logger.debug(`Updating edge job details: ${JSON.stringify(edgeJobDetails)}`);

      // Validate required fields
      if (!edgeJobDetails.currentJob) {
        throw new Error('ongoingJobExecution is required');
      }

      const { jobId, targets, jobStatus } = edgeJobDetails.currentJob;

      // Validate jobId - should not be null or empty
      if (!jobId || jobId.trim() === '') {
        throw new Error('jobId is required and cannot be null or empty');
      }

      // Validate targets array - should not be empty
      if (!targets || !Array.isArray(targets) || targets.length === 0) {
        throw new Error('targets array is required and cannot be empty');
      }

      if (jobStatus && !Object.values(JobStatus).includes(jobStatus as JobStatus)) {
        const validStatuses = Object.values(JobStatus).join(', ');
        throw new Error(`Invalid jobStatus. Must be one of: ${validStatuses}`);
      }

      // Find and update the edge by edgeId using the provided edge job details
      const updatedEdge = await EdgeModel.findOneAndUpdate(
        { edgeId }, // Filter by edgeId
        {
          $set: {
            currentJob: {
              ...edgeJobDetails.currentJob,
            },
          },
        },
        {
          new: true,
        },
      );

      if (!updatedEdge) {
        logger.warn(`No edge found with edgeId: ${edgeId}`);
        return null;
      }

      logger.debug(`Successfully updated edge details: ${JSON.stringify(updatedEdge.toObject())}`);
      return updatedEdge;
    } catch (error) {
      logger.error(`Error updating edge job details for edgeId ${edgeId}:`, { error });
      throw error;
    }
  }

  public static async checkActiveJob(thingId: string): Promise<boolean> {
    try {
      // Check in your database for any active job for this thing
      const activeJobCount = await EdgeModel.countDocuments({
        edgeId: thingId,
        'currentJob.jobStatus': {
          $in: [JobStatus.Queued, JobStatus.InProgress],
        },
      });

      return activeJobCount > 0;
    } catch (error) {
      logger.error(`Error checking active jobs for thing ${thingId}:`, { error });
      // Return true to be safe - prevent job creation if we can't verify
      return true;
    }
  }

  public async updateJobStatusAndVersion(edgeId: string, currentJobDetails: CurrentJob): Promise<any> {
    try {
      await this.init();
      const jobStatus = JobExecutionStatus[currentJobDetails.jobStatus as keyof typeof JobExecutionStatus] ?? null;
      logger.debug('Current job details', `${JSON.stringify(currentJobDetails)}`);

      // First, get the current document to access existing targets
      const currentEdgeDetails = await EdgeModel.findOne({ edgeId });

      // Start with existing targets or empty array if none exist
      const currentTargets = currentEdgeDetails?.targets || [];

      // Merge the existing targets with the new targets from currentJobDetails
      const mergedTargets: Targets[] = this.mergeTargets(currentTargets, currentJobDetails.targets);

      return await EdgeModel.findOneAndUpdate(
        { edgeId },
        {
          $set: {
            targets: mergedTargets,
            currentJob: {
              jobId: currentJobDetails.jobId,
              jobStatus,
              completedAt: currentJobDetails.completedAt,
            },
            updatedAt: new Date(),
          },
        },
      );
    } catch (error) {
      logger.error(`Error updating job status for edge ${edgeId}:`, { error });
      throw error;
    }
  }

  /**
   * Merges existing targets with new targets, preserving existing targets
   * that aren't in the new targets list and updating those that are.
   */
  private mergeTargets(existingTargets: Targets[], newTargets?: Targets[]): Targets[] {
    if (!Array.isArray(newTargets) || newTargets.length === 0) {
      return existingTargets;
    }

    // Create a map of existing targets by targetName for easy lookup
    const existingTargetsMap = new Map();
    if (Array.isArray(existingTargets)) {
      existingTargets.forEach((target: Targets) => {
        if (target && target.targetName) {
          existingTargetsMap.set(target.targetName, target);
        }
      });
    }

    // Update or add targets from newTargets
    newTargets.forEach((newTarget: Targets) => {
      if (newTarget && newTarget.targetName) {
        existingTargetsMap.set(newTarget.targetName, newTarget);
      }
    });

    // Convert map back to array
    return Array.from(existingTargetsMap.values());
  }
}
