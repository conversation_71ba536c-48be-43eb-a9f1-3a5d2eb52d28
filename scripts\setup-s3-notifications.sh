#!/bin/bash

# Script to setup S3 bucket notifications for firmware upload processing
# This script should be run after CDK deployment

set -e

# Configuration
STAGE=${1:-dev}
AWS_REGION=${AWS_REGION:-us-east-1}

# Bucket names (adjust based on your configuration)
CJC_BUCKET_NAME="cjc-backup-files-bucket-${STAGE}"
AHP_BUCKET_NAME="ahp-gateway-ota-${STAGE}"

# Lambda function name (adjust based on your CDK stack naming)
LAMBDA_FUNCTION_NAME="ahp-cjc-ota-job-UploadNotificationLambda-${STAGE}"

echo "Setting up S3 notifications for stage: ${STAGE}"
echo "AWS Region: ${AWS_REGION}"

# Get Lambda function ARN
echo "Getting Lambda function ARN..."
LAMBDA_ARN=$(aws lambda get-function \
  --function-name "${LAMBDA_FUNCTION_NAME}" \
  --region "${AWS_REGION}" \
  --query 'Configuration.FunctionArn' \
  --output text)

if [ -z "$LAMBDA_ARN" ]; then
  echo "Error: Could not find Lambda function: ${LAMBDA_FUNCTION_NAME}"
  echo "Please check the function name and ensure CDK deployment completed successfully"
  exit 1
fi

echo "Lambda ARN: ${LAMBDA_ARN}"

# Create temporary notification configuration files
TEMP_DIR=$(mktemp -d)
CJC_NOTIFICATION_FILE="${TEMP_DIR}/cjc-notification.json"
AHP_NOTIFICATION_FILE="${TEMP_DIR}/ahp-notification.json"

# Create CJC bucket notification configuration
cat > "${CJC_NOTIFICATION_FILE}" << EOF
{
  "LambdaConfigurations": [
    {
      "Id": "FirmwareUploadNotification",
      "LambdaFunctionArn": "${LAMBDA_ARN}",
      "Events": [
        "s3:ObjectCreated:*"
      ],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "prefix",
              "Value": "GatewayDocs/OTA/"
            },
            {
              "Name": "suffix",
              "Value": ".bin"
            }
          ]
        }
      }
    }
  ]
}
EOF

# Create AHP bucket notification configuration
cat > "${AHP_NOTIFICATION_FILE}" << EOF
{
  "LambdaConfigurations": [
    {
      "Id": "AHPFirmwareUploadNotification",
      "LambdaFunctionArn": "${LAMBDA_ARN}",
      "Events": [
        "s3:ObjectCreated:*"
      ],
      "Filter": {
        "Key": {
          "FilterRules": [
            {
              "Name": "suffix",
              "Value": ".bin"
            }
          ]
        }
      }
    }
  ]
}
EOF

# Function to setup bucket notification
setup_bucket_notification() {
  local bucket_name=$1
  local notification_file=$2
  local bucket_type=$3
  
  echo "Setting up notification for ${bucket_type} bucket: ${bucket_name}"
  
  # Check if bucket exists
  if ! aws s3api head-bucket --bucket "${bucket_name}" --region "${AWS_REGION}" 2>/dev/null; then
    echo "Warning: Bucket ${bucket_name} does not exist or is not accessible"
    return 1
  fi
  
  # Apply notification configuration
  if aws s3api put-bucket-notification-configuration \
    --bucket "${bucket_name}" \
    --notification-configuration "file://${notification_file}" \
    --region "${AWS_REGION}"; then
    echo "✅ Successfully configured notifications for ${bucket_name}"
  else
    echo "❌ Failed to configure notifications for ${bucket_name}"
    return 1
  fi
}

# Setup notifications for both buckets
echo ""
echo "Configuring S3 bucket notifications..."

setup_bucket_notification "${CJC_BUCKET_NAME}" "${CJC_NOTIFICATION_FILE}" "CJC"
setup_bucket_notification "${AHP_BUCKET_NAME}" "${AHP_NOTIFICATION_FILE}" "AHP"

# Cleanup
rm -rf "${TEMP_DIR}"

echo ""
echo "S3 notification setup completed!"
echo ""
echo "To verify the configuration:"
echo "aws s3api get-bucket-notification-configuration --bucket ${CJC_BUCKET_NAME}"
echo "aws s3api get-bucket-notification-configuration --bucket ${AHP_BUCKET_NAME}"
echo ""
echo "To test the setup:"
echo "1. Upload a .bin file to s3://${CJC_BUCKET_NAME}/GatewayDocs/OTA/"
echo "2. Check CloudWatch logs for the Lambda function: ${LAMBDA_FUNCTION_NAME}"
echo "3. Check AWS Signer console for new signing jobs"
