import * as aws from 'aws-sdk';
import { logger } from '../helpers';

const BUCKET_NAME = `${process.env.CJC_OTA_SERVICE_BUCKET_NAME}-${process.env.STAGE}`;
const EXPIRATION = process.env.S3_URL_EXPIRATION_TIME ? Number(process.env.S3_URL_EXPIRATION_TIME) : 3600;

export class S3Service {
  private s3: aws.S3;

  private bucketName: string;

  constructor(bucketName = BUCKET_NAME) {
    this.bucketName = bucketName;
    this.s3 = new aws.S3({
      region: process.env.AWS_REGION,
    });
  }

  public async generatePresignedUrl(filePath: string, operation: 'putObject' | 'getObject') {
    const params: any = {
      Bucket: this.bucketName,
      Key: filePath,
      Expires: EXPIRATION,
    };
    if (operation === 'putObject') {
      params.ContentType = 'application/octet-stream';
    }

    return this.s3.getSignedUrl(operation, params);
  }

  public async checkFileExistInS3(filePath: string): Promise<boolean> {
    try {
      await this.s3
        .headObject({
          Bucket: this.bucketName,
          Key: filePath,
        })
        .promise();
      return true;
    } catch (err: any) {
      logger.error(`Error checking file existence for path: ${filePath}. ${err.message}`);
      if (err.code === 'NoSuchKey') {
        throw new Error(`File not found in the s3 bucket: ${filePath}`);
      } else {
        throw new Error(`Error checking file existence for path: ${filePath}. ${err.message}`);
      }
    }
  }

  public async listFilesInS3(params: any, versionFilter?: string): Promise<{ prefixes: string[]; nextToken?: string }> {
    const listObjectParams: aws.S3.ListObjectsV2Request = {
      ...params,
      Bucket: this.bucketName,
      Delimiter: '/',
    };

    logger.info(`listObjectParams: ${JSON.stringify(listObjectParams)}`);
    logger.info(`versionFilter: ${versionFilter}`);

    const response = await this.s3.listObjectsV2(listObjectParams).promise();

    // Extract version folders from CommonPrefixes
    let versionPrefixes = (response.CommonPrefixes || [])
      .map((prefix) => {
        const prefixPath = prefix.Prefix || '';
        const pathSegments = prefixPath.split('/').filter(Boolean);
        return pathSegments[pathSegments.length - 1];
      })
      .filter((version) => version && version.length > 0);

    // Apply version filter if provided
    if (versionFilter) {
      versionPrefixes = versionPrefixes.filter((version) => this.matchesVersionFilter(version, versionFilter));
    }

    // Sort the filtered results
    versionPrefixes.sort((a, b) => a.toLowerCase().localeCompare(b.toLowerCase()));

    logger.info(`Filtered and sorted versions: ${JSON.stringify(versionPrefixes)}`);

    return {
      prefixes: versionPrefixes,
      nextToken: response.NextContinuationToken,
    };
  }

  private matchesVersionFilter(version: string, filter: string): boolean {
    // Remove V prefix if present for comparison
    const cleanVersion = version.startsWith('V') ? version.substring(1) : version;
    const versionParts = cleanVersion.toLowerCase().split('.');
    const filterParts = filter.toLowerCase().split('.');

    // If filter has more parts than version, no match
    if (filterParts.length > versionParts.length) {
      return false;
    }

    // Check each filter part against corresponding version part
    for (let i = 0; i < filterParts.length; i += 1) {
      const filterPart = filterParts[i];
      const versionPart = versionParts[i];

      if (!this.matchesPart(versionPart, filterPart)) {
        return false;
      }
    }

    return true;
  }

  private matchesPart(versionPart: string, filterPart: string): boolean {
    // Empty filter part matches anything
    if (!filterPart || filterPart === '*') {
      return true;
    }

    // Exact match
    if (versionPart === filterPart) {
      return true;
    }

    // Partial match - filter part is prefix of version part
    if (versionPart.startsWith(filterPart)) {
      return true;
    }

    // Extract numbers for numeric comparison
    const versionNum = this.extractNumber(versionPart);
    const filterNum = this.extractNumber(filterPart);

    if (versionNum !== null && filterNum !== null) {
      return versionNum === filterNum;
    }

    return false;
  }

  private extractNumber(part: string): number | null {
    const match = part.match(/(\d+)/);
    return match ? parseInt(match[1], 10) : null;
  }
}
