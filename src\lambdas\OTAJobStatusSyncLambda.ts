import 'reflect-metadata';
import { SQSEvent } from "aws-lambda";
import { logger } from '../helpers';
import { IotService } from '../services/IotService';
import { EdgeSyncService } from '../services/EdgeSyncService';
import { CurrentJob } from '../models/interfaces/EdgeSyncInterface';

export const handler = async (event : SQSEvent) =>{
     logger.info("Event received in Lambda", JSON.stringify(event, null, 2));
      const iotService = new IotService();
      const edgeSyncService = new EdgeSyncService()
     for (const record of event.Records) {
          try {
            const bodyDecoded = Buffer.from(record.body, "base64").toString("utf-8");
            const messageBody = JSON.parse(bodyDecoded);
            const {
                jobId,
                status,
                timestamp,
                thingArn
            } = messageBody
            logger.info("after parsing the mqttpayload recived on topic", JSON.stringify(messageBody, null, 2));
            const thingName = thingArn.split('/')?.[1];
            logger.info("Thing name", thingName);
            const gwId = thingName.replace(/^cjc-/, "");
            const targetVersions  = await iotService.getJobStatusAndTargetVersions(jobId);
            const currentJobDetails : CurrentJob = {
              jobId : jobId,
              jobStatus : status || null,
              completedAt : timestamp,
              targets : targetVersions
            } 
           await  edgeSyncService.updateJobStatusAndVersion(gwId,currentJobDetails)
           logger.info(`Updated job ${jobId} for thing ${thingName} with status ${status} and traget version`);
           logger.debug(`Updated job ${jobId} for thing ${thingName} with status ${status} and traget version`);
          } catch (err) {
            logger.error("Error while updating the job status", `${err}`);
          }
        }
}