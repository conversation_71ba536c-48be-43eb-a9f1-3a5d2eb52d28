<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts">
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_firmware" duration="3"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_filesystem" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for RX651_CHL" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return null for unknown file type" duration="1"/>
    <testCase name="CodeSigningService startSigningJob should start signing job successfully" duration="25">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;jobId&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'jobId')
    at CodeSigningService.jobId [as startSigningJob] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:78:25)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:91:22)]]></failure>
    </testCase>
    <testCase name="CodeSigningService startSigningJob should throw error when version is missing" duration="5"/>
    <testCase name="CodeSigningService startSigningJob should handle signing job failure" duration="8">
      <failure message="Error: expect(received).rejects.toThrow(expected)"><![CDATA[Error: expect(received).rejects.toThrow(expected)

Expected substring: "Signing failed"
Received message:   "Cannot read properties of undefined (reading 'jobId')"

      76 |
      77 |       logger.info('Signing job started successfully', {
    > 78 |         jobId: response.jobId,
         |                         ^
      79 |         source: `s3://${request.bucketName}/${request.key}`,
      80 |         destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
      81 |         profile: request.profileName,

      at CodeSigningService.jobId [as startSigningJob] (src/services/CodeSigningService.ts:78:25)
      at Object.<anonymous> (src/__tests__/services/CodeSigningService.test.ts:111:7)
    at Object.toThrow (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:218:22)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:113:17)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle empty job requests" duration="0"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should monitor and process successful jobs" duration="5">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;Succeeded&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'Succeeded')
    at CodeSigningService.Succeeded [as monitorBatchSigningJobs] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:153:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:162:7)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle failed jobs" duration="6">
      <failure message="Error: expect(received).resolves.not.toThrow()"><![CDATA[Error: expect(received).resolves.not.toThrow()

Received promise rejected instead of resolved
Rejected to value: [TypeError: Cannot read properties of undefined (reading 'Succeeded')]
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:177:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService monitorBatchSigningJobs should handle job status check errors" duration="5">
      <failure message="Error: expect(received).resolves.not.toThrow()"><![CDATA[Error: expect(received).resolves.not.toThrow()

Received promise rejected instead of resolved
Rejected to value: [TypeError: Cannot read properties of undefined (reading 'Succeeded')]
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:185:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService checkSigningJobStatus should return job status successfully" duration="0">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;status&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'status')
    at CodeSigningService.status [as checkSigningJobStatus] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:200:24)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:200:22)]]></failure>
    </testCase>
    <testCase name="CodeSigningService copySignedFileWithCustomName should copy and delete signed file successfully" duration="3">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: Any<CopyObjectCommand>

Number of calls: 0
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:229:33)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)]]></failure>
    </testCase>
    <testCase name="CodeSigningService copySignedFileWithCustomName should handle copy operation failure" duration="2">
      <failure message="Error: expect(received).rejects.toThrow()"><![CDATA[Error: expect(received).rejects.toThrow()

Received promise resolved instead of rejected
Resolved to value: undefined
    at expect (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\expect\build\index.js:113:15)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:240:13)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService generateCustomFileName should generate correct custom filename" duration="1"/>
    <testCase name="CodeSigningService generateCustomFileName should handle filename without extension" duration="3">
      <failure message="Error: expect(received).toBe(expected) // Object.is equality"><![CDATA[Error: expect(received).toBe(expected) // Object.is equality

Expected: "filename_signed.undefined"
Received: "filename_signed.filename"
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:261:22)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService delay should delay for specified time" duration="115"/>
  </file>
</testExecutions>