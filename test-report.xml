<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/lambdas/UpdateEdgeOngoingJobStatus.spec.ts">
    <testCase name="Lambda handler should process SQS event and update job status" duration="5">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: "cjc-603495952106228-324354235435316"

Number of calls: 0
    at Object.<anonymous> (/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/lambdas/UpdateEdgeOngoingJobStatus.spec.ts:81:41)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)]]></failure>
    </testCase>
    <testCase name="Lambda handler should handle errors gracefully" duration="0">
      <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: "Error in the while updating the job status", StringContaining "parse error"

Number of calls: 0
    at Object.<anonymous> (/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/lambdas/UpdateEdgeOngoingJobStatus.spec.ts:105:26)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)]]></failure>
    </testCase>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/resolver/CjcOtaJob.resolver.spec.ts">
    <testCase name="CjcOtaJobResolver describeFirmwareJob should handle errors from FirmwareJobService" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should describe firmware job for the given jobIds" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should initialize xray and handle error" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should handle successful describeFirmwareJob and close xray segment" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should handle errors from FirmwareJobService" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should describe firmware job execution for the given jobIds" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should initialize xray and handle errors during describeFirmwareJobExecution" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should handle successful describeFirmwareJobExecution and close xray segment" duration="1"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/service/FirmwareJob.spec.ts">
    <testCase name="FirmwareJobService describeFirmwareJob should return firmware job details for valid job IDs" duration="5"/>
    <testCase name="FirmwareJobService describeFirmwareJob should return invalid job response for invalid job IDs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJob should return invalid job response when an error occurs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should return firmware job execution details for valid job IDs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should return invalid job response for invalid job IDs" duration="0"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should handle errors during job execution fetch" duration="1"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/service/IotService.spec.ts">
    <testCase name="IotService should return the same instance every time" duration="0"/>
    <testCase name="IotService should send IoT job command successfully" duration="0"/>
    <testCase name="IotService should fetch new credentials if not already cached" duration="0"/>
    <testCase name="IotService should reuse cached credentials if they are not expired" duration="0"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/helpers/JobDocumentBuilder.test.ts">
    <testCase name="JobDocumentBuilder _checkJobFilesExistInS3 should check if files exist in S3" duration="5"/>
    <testCase name="JobDocumentBuilder _checkJobFilesExistInS3 should return true if the file exists in S3" duration="0"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should return files for bulksAll target directory" duration="1"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should return files for LTEmodule target directory" duration="0"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should throw an error for an unknown target directory" duration="2"/>
    <testCase name="JobDocumentBuilder _buildJobDocument should build a job document for the specified target directory and version" duration="0"/>
    <testCase name="JobDocumentBuilder createJobDocument should build a job document for the specified target directory and version" duration="1"/>
    <testCase name="JobDocumentBuilder createJobDocument should throw an error if LTE module URL is not provided for LTEmodule target directory" duration="1"/>
    <testCase name="JobDocumentBuilder createJobDocument should throw an error if S3 throws an unexpected error" duration="0"/>
    <testCase name="JobDocumentBuilder _createFileInfo should create file info correctly" duration="1"/>
    <testCase name="JobDocumentBuilder _generateFileUrl should generate the correct file URL" duration="0"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/service/EventService.test.ts">
    <testCase name="EventService should create a new event successfully" duration="0"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/service/UploadCjcFirmwareService.test.ts">
    <testCase name="UploadCjcFirmwareService should return success and a presigned URL for a valid request with overwrite enabled" duration="1"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/service/StsConfigService.test.ts">
    <testCase name="StsConfigService should successfully create STS credentials" duration="1"/>
    <testCase name="StsConfigService should refresh STS credentials if expired" duration="0"/>
    <testCase name="StsConfigService should get new STS credentials if no cached credentials" duration="0"/>
  </file>
  <file path="/Users/<USER>/tcc/ahp-service-cjc-ota-job/src/__tests__/utils/x-ray-util.test.ts">
    <testCase name="X-ray-util::getTraceHeader should get header value" duration="1"/>
    <testCase name="X-ray-util::getTraceHeader should be initialised" duration="1"/>
    <testCase name="X-ray-util::getTraceHeader should be handleErrors" duration="1"/>
  </file>
</testExecutions>