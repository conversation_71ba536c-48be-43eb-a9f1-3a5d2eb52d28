<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts">
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_firmware" duration="3"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for ESP32_filesystem" duration="0"/>
    <testCase name="CodeSigningService getSigningProfile should return correct profile for RX651_CHL" duration="1"/>
    <testCase name="CodeSigningService getSigningProfile should return null for unknown file type" duration="1"/>
    <testCase name="CodeSigningService startSigningJob should start signing job successfully" duration="52">
      <failure message="TypeError: this.signerClient.send is not a function"><![CDATA[TypeError: this.signerClient.send is not a function
    at CodeSigningService.send [as startSigningJob] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:75:48)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:106:47)
    at Promise.then.completed (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\node_modules\jest-runner\build\runTest.js:444:34)]]></failure>
    </testCase>
    <testCase name="CodeSigningService startSigningJob should throw error when version is missing" duration="4"/>
    <testCase name="CodeSigningService monitorBatchSigningJobs should monitor and process successful jobs" duration="6">
      <failure message="TypeError: Cannot read properties of undefined (reading &apos;Succeeded&apos;)"><![CDATA[TypeError: Cannot read properties of undefined (reading 'Succeeded')
    at CodeSigningService.Succeeded [as monitorBatchSigningJobs] (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\services\CodeSigningService.ts:153:46)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.<anonymous> (C:\Carrier WorkSpace\AHP REPO\ahp-service-cjc-ota-job\src\__tests__\services\CodeSigningService.test.ts:160:7)]]></failure>
    </testCase>
    <testCase name="CodeSigningService delay should delay for specified time" duration="104"/>
  </file>
</testExecutions>