<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/utils/x-ray-util.test.ts">
    <testCase name="X-ray-util::getTraceHeader should get header value" duration="3"/>
    <testCase name="X-ray-util::getTraceHeader should be initialised" duration="19"/>
    <testCase name="X-ray-util::getTraceHeader should be handleErrors" duration="2"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/EdgeSyncService.test.ts">
    <testCase name="EdgeSyncService syncAllEdgesToMongo should return null if config is not provided" duration="2"/>
    <testCase name="EdgeSyncService syncAllEdgesToMongo should process edges in batches and return sync results" duration="103"/>
    <testCase name="EdgeSyncService syncAllEdgesToMongo should handle errors during sync process" duration="705"/>
    <testCase name="EdgeSyncService updateEdgeDataByEdgeId should update edge job details successfully" duration="0"/>
    <testCase name="EdgeSyncService updateEdgeDataByEdgeId should throw error when jobId is missing" duration="5"/>
    <testCase name="EdgeSyncService updateEdgeDataByEdgeId should throw error when targets array is empty" duration="0"/>
    <testCase name="EdgeSyncService updateEdgeDataByEdgeId should throw error when jobStatus is invalid" duration="1"/>
    <testCase name="EdgeSyncService updateEdgeDataByEdgeId should return null when edge is not found" duration="0"/>
    <testCase name="EdgeSyncService checkActiveJob should return true when active job exists" duration="0"/>
    <testCase name="EdgeSyncService checkActiveJob should return false when no active job exists" duration="0"/>
    <testCase name="EdgeSyncService checkActiveJob should return true when error occurs during check" duration="0"/>
    <testCase name="EdgeSyncService updateJobStatusAndVersion should update job status and merge targets correctly" duration="0"/>
    <testCase name="EdgeSyncService updateJobStatusAndVersion should handle missing targets in currentJobDetails" duration="0"/>
    <testCase name="EdgeSyncService updateJobStatusAndVersion should handle edge not found in database" duration="0"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/resolver/CjcOtaJob.resolver.spec.ts">
    <testCase name="CjcOtaJobResolver describeFirmwareJob should handle errors from FirmwareJobService" duration="8"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should describe firmware job for the given jobIds" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should initialize xray and handle error" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJob should handle successful describeFirmwareJob and close xray segment" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should handle errors from FirmwareJobService" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should describe firmware job execution for the given jobIds" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should initialize xray and handle errors during describeFirmwareJobExecution" duration="1"/>
    <testCase name="CjcOtaJobResolver describeFirmwareJobExecution should handle successful describeFirmwareJobExecution and close xray segment" duration="1"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/lambdas/OTAJobStatusSyncLambda.spec.ts">
    <testCase name="OTAJobStatusSyncLambda Handler should process a valid SQS event for CJC thing" duration="1"/>
    <testCase name="OTAJobStatusSyncLambda Handler should skip non-CJC things and return early" duration="0"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle case when targetVersions is null/undefined" duration="1"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle case when status is null/undefined" duration="0"/>
    <testCase name="OTAJobStatusSyncLambda Handler should log error if body is not valid base64" duration="0"/>
    <testCase name="OTAJobStatusSyncLambda Handler should log error if JSON is malformed" duration="0"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle error when thingArn split fails" duration="1"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle multiple records with mixed CJC and non-CJC things" duration="0"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle service errors gracefully" duration="1"/>
    <testCase name="OTAJobStatusSyncLambda Handler should handle EdgeSyncService errors gracefully" duration="0"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/FirmwareJob.spec.ts">
    <testCase name="FirmwareJobService describeFirmwareJob should return firmware job details for valid job IDs" duration="10"/>
    <testCase name="FirmwareJobService describeFirmwareJob should return invalid job response for invalid job IDs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJob should return invalid job response when an error occurs" duration="2"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should return firmware job execution details for valid job IDs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should return invalid job response for invalid job IDs" duration="1"/>
    <testCase name="FirmwareJobService describeFirmwareJobExecution should handle errors during job execution fetch" duration="1"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/helpers/JobDocumentBuilder.test.ts">
    <testCase name="JobDocumentBuilder _checkJobFilesExistInS3 should check if files exist in S3" duration="1"/>
    <testCase name="JobDocumentBuilder _checkJobFilesExistInS3 should return true if the file exists in S3" duration="0"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should return files for bulksAll target directory" duration="1"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should return files for LTEmodule target directory" duration="0"/>
    <testCase name="JobDocumentBuilder _getFilesForTargetDirectory should throw an error for an unknown target directory" duration="1"/>
    <testCase name="JobDocumentBuilder _buildJobDocument should build a job document for the specified target directory and version" duration="0"/>
    <testCase name="JobDocumentBuilder createJobDocument should build a job document for the specified target directory and version" duration="1"/>
    <testCase name="JobDocumentBuilder createJobDocument should throw an error if LTE module URL is not provided for LTEmodule target directory" duration="1"/>
    <testCase name="JobDocumentBuilder createJobDocument should throw an error if S3 throws an unexpected error" duration="0"/>
    <testCase name="JobDocumentBuilder _createFileInfo should create file info correctly" duration="1"/>
    <testCase name="JobDocumentBuilder _generateFileUrl should generate the correct file URL" duration="0"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/IotService.spec.ts">
    <testCase name="IotService should return the same instance every time" duration="3"/>
    <testCase name="IotService should send IoT job command successfully" duration="0"/>
    <testCase name="IotService should fetch new credentials if not already cached" duration="0"/>
    <testCase name="IotService should reuse cached credentials if they are not expired" duration="1"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/UploadCjcFirmwareService.test.ts">
    <testCase name="UploadCjcFirmwareService should return success and a presigned URL for a valid request with overwrite enabled" duration="1"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/EventService.test.ts">
    <testCase name="EventService should create a new event successfully" duration="0"/>
  </file>
  <file path="/Users/<USER>/Carrier/BitBucket/ahp-service-cjc-ota-job/src/__tests__/services/StsConfigService.test.ts">
    <testCase name="StsConfigService should successfully create STS credentials" duration="0"/>
    <testCase name="StsConfigService should refresh STS credentials if expired" duration="0"/>
    <testCase name="StsConfigService should get new STS credentials if no cached credentials" duration="0"/>
  </file>
</testExecutions>