/* eslint-disable no-await-in-loop */
import { logger } from '../helpers';
import {
  BASE_S3_OTA_FILE_PATH,
  Edge,
  FilterInput,
  JobIdSearchParams,
  OtaGatewayJobInfo,
  OtaMetadataResponse,
  Targets,
  VersionAvailabilityInfo,
} from '../models';
import { MongoDBAdapter } from '../adapters';
import { EdgeModel } from '../schemas';
import { Connectivity } from '../enum';
import { S3Service } from './S3Service';
import { IotService } from './IotService';

export class OTAMetaDataService {
  private readonly mongoDb: MongoDBAdapter;

  private readonly s3Service: S3Service;

  private readonly iotService: IotService;

  constructor() {
    this.mongoDb = new MongoDBAdapter();
    this.s3Service = new S3Service();
    this.iotService = IotService.getInstance();
  }

  private async init() {
    await this.mongoDb.connect();
  }

  /**
   * Get all edges with pagination from alleadges collection
   */
  public async getOTAMetaData(filters: FilterInput, page: number, limit: number): Promise<OtaMetadataResponse> {
    try {
      if (!page || page < 1) {
        throw new Error(`Invalid page parameter provided: ${page}`);
      }

      if (!limit || limit < 1) {
        throw new Error(`Invalid limit parameter provided: ${limit}`);
      }

      await this.init();

      const sortField = 'updatedAt';
      const sortDirection = 1; // setting 1 for 'asc'
      // Build query conditions
      const conditions: any[] = [];

      // Search term filter (OR condition for multiple search terms)
      if (filters?.searchTerm?.length) {
        const searchConditions = filters.searchTerm
          .filter((term) => term?.trim()) // Remove empty/null terms
          .flatMap((term) => [
            { siteName: { $regex: term.trim(), $options: 'i' } },
            { edgeId: { $regex: term.trim(), $options: 'i' } },
          ]);

        if (searchConditions.length > 0) {
          conditions.push({ $or: searchConditions });
        }
      }

      // Connectivity filter
      if (filters?.connectivity?.length) {
        const connectivityValues = filters.connectivity.map((connection) => connection === Connectivity.Online);
        conditions.push({ isOnline: { $in: connectivityValues } });
      }

      // Target name filter
      if (filters?.targetName?.length) {
        conditions.push({ 'currentJob.targets.targetName': { $in: filters.targetName } });
      }

      // Job status filter
      if (filters?.jobStatus?.length) {
        conditions.push({ 'currentJob.jobStatus': { $in: filters.jobStatus } });
      }

      // Job ID filter
      if (filters?.jobId?.length) {
        conditions.push({ 'currentJob.jobId': { $in: filters.jobId } });
      }

      // Combine all conditions with AND
      const query = conditions.length > 0 ? { $and: conditions } : {};

      // Get total count
      const totalRecords = await EdgeModel.countDocuments(query);

      // Preparing pagination
      const skip = (page - 1) * limit;
      const edges: Edge[] = (await EdgeModel.find(query)
        .sort({ [sortField]: sortDirection })
        .skip(skip)
        .limit(limit)
        .lean()
        .exec()) as Edge[];

      const transformedEdges = this.transformMongoDataToOtaResponse(edges);
      const totalPages = Math.ceil(totalRecords / limit);

      return {
        data: transformedEdges,
        totalRecords,
        currentPage: page,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      };
    } catch (error) {
      logger.error('Error getting OTA metadata:', { error });
      throw error;
    }
  }

  private transformMongoDataToOtaResponse(edges: Edge[]): OtaGatewayJobInfo[] {
    return edges.map((edge) => {
      return {
        thingId: edge.edgeId,
        siteName: edge.siteName || '',
        connectivity: edge.isOnline ? Connectivity.Online : Connectivity.Offline,
        targets:
          edge.targets.map((target) => ({ targetName: target.targetName, currentVersion: target.version })) || [],
        jobId: edge.currentJob?.jobId || null,
        jobStatus: edge.currentJob?.jobStatus || null,
        ongoingJobExecutions: {
          completedAt: edge.currentJob?.completedAt || null,
          targets:
            edge.currentJob?.targets.map((target: Targets) => ({
              targetName: target.targetName,
              updateVersion: target.version,
            })) || [],
        },
      } as OtaGatewayJobInfo;
    });
  }

  public async getTargetVersionsFromS3({
    targetDirectory,
    limit,
    continuationToken,
    versionFilter,
  }: {
    targetDirectory: string;
    limit: number;
    continuationToken?: string;
    versionFilter?: string;
  }): Promise<{ versionDetails: VersionAvailabilityInfo[]; nextToken?: string }> {
    const filePath = `${BASE_S3_OTA_FILE_PATH}${targetDirectory}/`;

    const params = {
      Prefix: filePath,
      Delimiter: '/',
      MaxKeys: limit,
      ContinuationToken: continuationToken,
    };

    const { prefixes, nextToken } = await this.s3Service.listFilesInS3(params, versionFilter);

    const versions: any = prefixes
      .map((currentPrefix) => currentPrefix.replace(filePath, '').replace(/\/$/, ''))
      .filter(Boolean)
      .sort((a, b) => a.localeCompare(b, undefined, { numeric: true }));
    // For each version, list the files in that directory
    const versionDetailsPromises = versions.map(async (version: string) => {
      const versionPath = `${filePath}${version}/`;

      try {
        // List objects in this version directory
        const listParams = {
          Prefix: versionPath,
        };

        // Use the existing listObjectsInDirectory method
        const filesResponse = await this.s3Service.listObjectsInDirectory(listParams);
        logger.debug(`filesResponse ::: ${JSON.stringify(filesResponse)}`);

        const files = filesResponse
          .filter((item: any) => item.Key && !item.Key.endsWith('/')) // Filter out directories
          .map((item: any) => {
            return item.Key?.split('/').pop().split('.')[0] || ''; // Return the enum value if found, otherwise the filename
          })
          .filter(Boolean);

        return {
          version,
          availableFiles: files,
        };
      } catch (error) {
        logger.error(`Error listing files for version ${version}:`, { error });
        return {
          version,
          availableFiles: [],
        };
      }
    });

    const versionDetails = await Promise.all(versionDetailsPromises);

    return { versionDetails, nextToken };
  }

  public async getJobIds({ search = '', page = 1, limit = 10 }: JobIdSearchParams) {
    try {
      const skipCount = (page - 1) * limit;
      const filter: Record<string, any> = {};

      if (search.trim()) {
        filter['currentJob.jobId'] = { $regex: search, $options: 'i' };
      }

      const [matchingEdges, totalCount] = await Promise.all([
        EdgeModel.find(filter).select('currentJob.jobId -_id').skip(skipCount).limit(limit).lean(),
        EdgeModel.countDocuments(filter),
      ]);

      const jobIdList = matchingEdges
        .map((edge) => edge.currentJob?.jobId)
        .filter((jobId): jobId is string => typeof jobId === 'string');

      return {
        total: totalCount,
        page,
        pageSize: limit,
        totalPages: Math.ceil(totalCount / limit),
        jobIds: jobIdList,
      };
    } catch (error) {
      logger.error('Error getting job IDs:', { error });
      throw error;
    }
  }
}
