import {
  TargetDirectories,
  TargetFirmwareNames,
  TargetFirmwarePriorities,
  TargetFirmwareWithExtentions,
  TargetUpdateOperations,
} from '../enum';
import {
  FileInfo,
  JobDocument,
  FileAndJobNameMapper,
  BASE_S3_OTA_FILE_PATH,
  TargetVersions,
  FirmwareToDirectoryMap,
  LTEModuleFileInfo,
  Targets,
} from '../models';
import { S3Service } from '../services/S3Service';

import { logger } from './logger';

export class JobDocumentBuilder {
  private readonly _s3Service: S3Service;

  constructor() {
    this._s3Service = new S3Service();
  }

  private async _checkJobFilesExistInS3(files: FileInfo[]): Promise<void> {
    const checkPromises = files.map((file) =>
      this._s3Service.checkFileExistInS3(`${BASE_S3_OTA_FILE_PATH}${file.moduleName}/${file.version}/${file.filename}`),
    );
    await Promise.all(checkPromises);
  }

  private async _generateFileUrl(
    targetDirectory: TargetDirectories,
    version: string,
    filename: string,
  ): Promise<string> {
    const filepath = `${BASE_S3_OTA_FILE_PATH}${targetDirectory}/${version}/${filename}`;
    return this._s3Service.generatePresignedUrl(filepath, 'getObject');
  }

  private async _createFileInfo(
    targetDirectory: TargetDirectories,
    version: string,
    filename: string,
    firmwareName: string,
    priority?: number,
  ): Promise<FileInfo> {
    const fileInfo = {
      filename,
      url: await this._generateFileUrl(targetDirectory, version, filename),
      version,
      priority,
      moduleName: targetDirectory,
      firmwareName,
    };

    if (!priority) {
      return fileInfo;
    }

    return { ...fileInfo, priority };
  }

  private async _getFilesForTargetDirectory(
    targetOperation: TargetUpdateOperations,
    targetVersions: TargetVersions[],
    lteModuleUrl?: string,
  ): Promise<FileInfo[] | LTEModuleFileInfo[]> {
    if (!Object.values(TargetUpdateOperations).includes(targetOperation)) {
      throw new Error(`Unknown target operation: ${targetOperation}`);
    }

    if (targetOperation === TargetUpdateOperations.LTEmodule && lteModuleUrl) {
      return [
        {
          url: lteModuleUrl,
          moduleName: TargetFirmwareNames.LTEmodule,
        },
      ];
    }

    return Promise.all(
      targetVersions.map(async (currentTargetFirmware: TargetVersions) => {
        const { version, firmwareName } = currentTargetFirmware;
        return this._createFileInfo(
          FirmwareToDirectoryMap[firmwareName],
          version,
          TargetFirmwareWithExtentions[firmwareName],
          firmwareName,
          TargetFirmwarePriorities[firmwareName as keyof typeof TargetFirmwarePriorities] || undefined,
        );
      }),
    );
  }

  private getFirmwareVersion(targetVersions: TargetVersions[], firmwareName: string): string {
    if (firmwareName === TargetFirmwareNames.LTEmodule) {
      return '';
    }

    if (targetVersions.length === 0) {
      throw new Error('No target versions provided');
    }

    if (!firmwareName) {
      throw new Error('Firmware name is required to get version');
    }
    // Assuming we want the first version for the job document
    return targetVersions.filter((currentTargetFirmware) => currentTargetFirmware.firmwareName === firmwareName)[0]
      .version;
  }

  private _buildJobDocument(
    targetUpdateOperation: TargetUpdateOperations,
    targetVersions: TargetVersions[],
    files: FileInfo[],
  ): JobDocument {
    return {
      version: '', // this will be set as thing id later once the job is created
      steps: [
        {
          action: {
            name: targetUpdateOperation,
            type: 'runHandler',
            input: {
              handler: 'CJC-EYE_Handler',
              args: [
                Object.fromEntries(
                  files.map((file: FileInfo) => {
                    const mappedFileName =
                      file.moduleName === TargetDirectories.LTEmodule
                        ? FileAndJobNameMapper[file?.moduleName]
                        : FileAndJobNameMapper[file?.filename];
                    const currentFirmwareName =
                      file.moduleName === TargetFirmwareNames.LTEmodule
                        ? TargetFirmwareNames.LTEmodule
                        : file.firmwareName;
                    return [
                      mappedFileName,
                      {
                        filename: file.filename,
                        url: file.url,
                        version: this.getFirmwareVersion(targetVersions, currentFirmwareName || ''),
                        ...(file.priority && { priority: file.priority }),
                      },
                    ];
                  }),
                ),
              ],
            },
          },
        },
      ],
    };
  }

  // Validation method
  private validateLteModuleUrl(url?: string): void {
    const LTE_MODULE_URL_REGEX = /^https?:\/\/harvest-files\.soracom\.io\/.*\.zip$/;

    if (!url || url.trim() === '') {
      throw new Error('LTE module URL must be provided when LTEmodule is included in target versions.');
    }

    if (!this.isValidLteModuleUrl(url, LTE_MODULE_URL_REGEX)) {
      throw new Error(`Invalid LTE Module URL format. Must be from harvest-files.soracom.io and end with .zip: ${url}`);
    }
  }

  private isValidLteModuleUrl = (url: string, regex: RegExp): boolean => {
    return regex.test(url);
  };

  public async createJobDocument(
    targetUpdateOperation: TargetUpdateOperations,
    targetVersions: TargetVersions[],
    lteModuleUrl?: string,
  ) {
    try {
      const targetDirectories = targetVersions.map(
        (currentTargetFirmware) =>
          TargetDirectories[currentTargetFirmware.firmwareName as keyof typeof TargetDirectories],
      );

      const hasLteModule = targetDirectories.includes(TargetDirectories.LTEmodule);

      // Validate LTE module requirements
      if (hasLteModule) {
        this.validateLteModuleUrl(lteModuleUrl);
      }

      const files: any = await this._getFilesForTargetDirectory(targetUpdateOperation, targetVersions, lteModuleUrl);

      // Only check S3 files if not using external LTE module URL
      if (!lteModuleUrl) {
        await this._checkJobFilesExistInS3(files);
      }

      return this._buildJobDocument(targetUpdateOperation, targetVersions, files);
    } catch (error: any) {
      logger.error(`Error in createJobDocument: ${error.message}`);
      throw new Error(`Error in createJobDocument: ${error.message}`);
    }
  }

  public getTargetVersionFromJobDocument(jobDocument : string){
    const parsedJobDocument = JSON.parse(jobDocument);

    if (!parsedJobDocument.steps || !Array.isArray(parsedJobDocument.steps) || (Array.isArray(parsedJobDocument.steps) && !parsedJobDocument.steps.length)) {
      throw new Error('Invalid job document format: missing steps');
    }
  
    const targets: Targets[] = [];

    for (const step of parsedJobDocument.steps) {
      const args = step?.action?.input?.args;
      if (!Array.isArray(args)) continue;
  
      for (const arg of args) {
        const fileKey = Object.keys(arg)[0];
        const fileData = arg[fileKey];
        logger.info("file Data", fileData);
        const filename = fileData?.filename.split('.')?.[0];
        logger.info("filename", filename);
        const version = fileData?.version;
  
        if (!filename || !version) continue;
  
        const isAllowed = Object.values(TargetFirmwareNames).includes(filename as TargetFirmwareNames);
        if (!isAllowed) {
          throw new Error(`Disallowed target name: ${filename}`);
        }
  
        targets.push({ targetName : filename, version });

        logger.info("target", JSON.stringify(targets));
      }
    }
    return targets;
  }
  
}


