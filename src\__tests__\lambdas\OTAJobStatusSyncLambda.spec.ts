import { SQSEvent } from 'aws-lambda';
import { handler } from '../../lambdas/OTAJobStatusSyncLambda';
import { IotService } from '../../services/IotService';
import { EdgeSyncService } from '../../services/EdgeSyncService';
import { logger } from '../../helpers'

jest.mock('../../services/IotService');
jest.mock('../../services/EdgeSyncService');
jest.mock('../../helpers');

const mockIotService = {
  getJobStatusAndTargetVersions: jest.fn(),
};

const mockEdgeSyncService = {
  updateJobStatusAndVersion: jest.fn(),
};

(IotService as unknown as jest.Mock).mockImplementation(() => mockIotService);
(EdgeSyncService as unknown as jest.Mock).mockImplementation(() => mockEdgeSyncService);

describe('Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const base64Payload = Buffer.from(JSON.stringify({
    jobId: 'job-123',
    status: 'COMPLETED',
    timestamp: '2025-08-25T12:38:02.902Z',
    thingArn: 'arn:aws:iot:us-east-1:123456789012:thing/cjc-123456'
  })).toString('base64');

  const mockEvent: SQSEvent = {
    Records: [
      {
        body: base64Payload,
        messageId: '1',
        receiptHandle: '',
        attributes: {
          ApproximateReceiveCount: '',
          SentTimestamp: '',
          SenderId: '',
          ApproximateFirstReceiveTimestamp: ''
        },
        messageAttributes: {},
        md5OfBody: '',
        eventSource: '',
        eventSourceARN: '',
        awsRegion: '',
      },
    ],
  };

  it('should process a valid SQS event', async () => {
    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: '**********' },
    ]);

    await handler(mockEvent);

    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledWith('job-123');
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledWith('123456', {
      jobId: 'job-123',
      jobStatus: 'COMPLETED',
      completedAt: '2025-08-25T12:38:02.902Z',
      targets: [
        { targetName: 'AmazonRootCA1', version: '**********' },
      ],
    });
  });

  it('should log error if body is not base64', async () => {
    const eventWithInvalidBody = {
      Records: [{ ...mockEvent.Records[0], body: 'not-a-base64' }],
    };

    await handler(eventWithInvalidBody as unknown as SQSEvent);

    expect(logger.error).toHaveBeenCalledWith('Error while updating the job status', expect.any(String));
    expect(mockEdgeSyncService.updateJobStatusAndVersion).not.toHaveBeenCalled();
  });

  it('should log error if JSON is malformed', async () => {
    const badJson = Buffer.from('{"invalidJson": }').toString('base64');
    const event = { Records: [{ ...mockEvent.Records[0], body: badJson }] };

    await handler(event as unknown as SQSEvent);

    expect(logger.error).toHaveBeenCalledWith('Error while updating the job status', expect.any(String));
  });

  it('should skip record if required fields are missing', async () => {
    const badPayload = Buffer.from(JSON.stringify({ status: 'COMPLETED' })).toString('base64');
    const event = { Records: [{ ...mockEvent.Records[0], body: badPayload }] };

    await handler(event as unknown as SQSEvent);

    expect(logger.error).toHaveBeenCalledWith('Error while updating the job status', expect.any(String));
  });

  it('should handle multiple records', async () => {
    mockIotService.getJobStatusAndTargetVersions.mockResolvedValue([
      { targetName: 'AmazonRootCA1', version: '**********' },
    ]);

    const secondRecord = {
      ...mockEvent.Records[0],
      messageId: '2',
    };

    const multiEvent = { Records: [mockEvent.Records[0], secondRecord] };

    await handler(multiEvent as SQSEvent);

    expect(mockIotService.getJobStatusAndTargetVersions).toHaveBeenCalledTimes(2);
    expect(mockEdgeSyncService.updateJobStatusAndVersion).toHaveBeenCalledTimes(2);
  });
});
