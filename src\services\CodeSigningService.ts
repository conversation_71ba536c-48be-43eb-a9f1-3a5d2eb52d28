import {
  SignerClient,
  StartSigningJobCommand,
  DescribeSigningJobCommand,
} from '@aws-sdk/client-signer';
import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import {
  codeSigningProfiles,
  SigningJobStatus,
} from '../enum/CjcFirmawareEnum';
import { checkSigningJobStatus } from '../models/interfaces/JobDocumentBuilder';
import { logger } from '../helpers/logger';

interface SigningJobRequest {
  bucketName: string;
  key: string;
  version?: string;
  profileName: string;
  destinationPrefix: string;
}
export class CodeSigningService {
  private signerClient: SignerClient;
  private s3Client: S3Client;

  constructor() {
    this.signerClient = new SignerClient({
      region: process.env.AWS_REGION || 'us-east-1',
    });
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
    });
  }

  public async startSigningJob(
    request: SigningJobRequest,
  ): Promise<string | undefined> {
    logger.info('Starting signing job', {
      bucket: request.bucketName,
      key: request.key,
      version: request.version,
      profile: request.profileName,
      destinationPrefix: request.destinationPrefix,
    });
    if (!request.version) {
      throw new Error('version is required for StartSigningJobCommand');
    }

    const command = new StartSigningJobCommand({
      source: {
        s3: {
          bucketName: request.bucketName,
          key: request.key,
          version: request.version,
        },
      },
      destination: {
        s3: {
          bucketName: request.bucketName,
          prefix: request.destinationPrefix,
        },
      },
      profileName: request.profileName,
    });

    try {
      const response = await this.signerClient.send(command);

      logger.info('Signing job started successfully', {
        jobId: response.jobId,
        source: `s3://${request.bucketName}/${request.key}`,
        destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
        profile: request.profileName,
      });

      // Return job ID for batch monitoring
      return response.jobId;
    } catch (error: any) {
      logger.error('Failed to start signing job', {
        error: error.message,
        source: `s3://${request.bucketName}/${request.key}`,
        profile: request.profileName,
      });
      throw error;
    }
  }

  public async monitorBatchSigningJobs(
    jobRequests: Array<{ jobId: string; request: SigningJobRequest }>,
  ): Promise<void> {
    if (jobRequests.length === 0) return;

    logger.info('Starting batch monitoring for signing jobs', {
      jobCount: jobRequests.length,
      jobIds: jobRequests.map((jr) => jr.jobId),
    });

    const maxRetries = 24;
    const pollInterval = 10000; // 10 seconds

    const pendingJobs = new Map(
      jobRequests.map((data) => [data.jobId, data.request]),
    );

    for (
      let attempt = 1;
      attempt <= maxRetries && pendingJobs.size > 0;
      attempt += 1
    ) {
      logger.info(`Batch monitoring attempt ${attempt}/${maxRetries}`, {
        pendingJobs: pendingJobs.size,
        jobIds: Array.from(pendingJobs.keys()),
      });

      const jobStatusPromises = Array.from(pendingJobs.keys()).map(
        async (jobId) => {
          try {
            //fetching job status for each job id
            const status: checkSigningJobStatus =
              await this.checkSigningJobStatus(jobId);
            return {
              jobId,
              status,
              request: pendingJobs.get(jobId)!,
            };
          } catch (err: any) {
            logger.error('Error checking job status in batch', {
              jobId,
              error: err.message,
            });
            return {
              jobId,
              status: { status: 'Error' },
              request: pendingJobs.get(jobId)!,
            };
          }
        },
      );

      //waiting till all job status are fetched
      const jobStatuses = await Promise.all(jobStatusPromises);

      for (const { jobId, status, request } of jobStatuses) {
        if (
          status.status === SigningJobStatus.Succeeded &&
          status.signedObjectLocation
        ) {
          logger.debug('Signing job succeeded', {
            jobId,
            signedObjectLocation: status.signedObjectLocation,
          });
          try {
            await this.copySignedFileWithCustomName(
              status.signedObjectLocation,
              request,
            );
            pendingJobs.delete(jobId);
          } catch (error: any) {
            logger.error('Error copying signed file to custom name', {
              error: error.message,
              jobId,
            });
            pendingJobs.delete(jobId);
          }
        } else if (status.status === SigningJobStatus.Failed) {
          logger.error('Job failed in batch', {
            jobId,
            statusReason: status.statusReason,
          });
          pendingJobs.delete(jobId); // Remove failed job
        }
      }
      if (pendingJobs.size > 0 && attempt < maxRetries) {
        await this.delay(pollInterval);
      }
    }

    if (pendingJobs.size > 0) {
      logger.warn('Some signing jobs timed out during batch monitoring', {
        timedOutJobs: Array.from(pendingJobs.keys()),
      });
    }
  }

  private async checkSigningJobStatus(
    jobId: string,
  ): Promise<checkSigningJobStatus> {
    const command = new DescribeSigningJobCommand({ jobId });
    const response = await this.signerClient.send(command);

    return {
      status: response.status || 'Unknown',
      statusReason: response.statusReason,
      signedObjectLocation: response.signedObject?.s3?.key,
    };
  }

  public getSigningProfile(
    objectKey: string,
    bucketName: string,
  ): string | null {
    // Frm Database we will be importing in future
    for (const [fileType, profile] of Object.entries(codeSigningProfiles)) {
      if (objectKey.includes(fileType)) {
        return profile;
      }
    }
    return null;
  }

  private async copySignedFileWithCustomName(
    signedObjectKey: string,
    originalRequest: SigningJobRequest,
  ): Promise<void> {
    const customFileName = this.generateCustomFileName(originalRequest.key);
    const basePath = originalRequest.key.substring(0, originalRequest.key.lastIndexOf('/'));
    const customKey = `${basePath}/signed/${customFileName}`;

    logger.info('Copying signed file to custom name', {
      from: signedObjectKey,
      to: customKey,
    });

    try {
      // Copy the signed file with custom name
      const copyCommand = new CopyObjectCommand({
        Bucket: originalRequest.bucketName,
        CopySource: `${originalRequest.bucketName}/${signedObjectKey}`,
        Key: customKey,
      });

      await this.s3Client.send(copyCommand);

      // Optionally delete the UUID-named file
      const deleteCommand = new DeleteObjectCommand({
        Bucket: originalRequest.bucketName,
        Key: signedObjectKey,
      });

      await this.s3Client.send(deleteCommand);

      logger.debug('Successfully copied and cleaned up signed file', {
        customKey,
        originalKey: signedObjectKey,
      });
    } catch (error: any) {
      logger.error('Error copying signed file to custom name', {
        error: error.message,
        from: signedObjectKey,
        to: customKey,
      });
      throw error;
    }
  }

  private generateCustomFileName(originalKey: string): string {
    //original file name
    const pathParts = originalKey.split('/');
    const fileName = pathParts[pathParts.length - 1];
    const fileNameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    const extension = fileName.split('.').pop();
    return `${fileNameWithoutExt}_signed.${extension}`;
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
