import { SignerClient, StartSigningJobCommand, DescribeSigningJobCommand } from '@aws-sdk/client-signer';
import { S3Client, CopyObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { codeSigningProfiles } from '../enum/CjcFirmawareEnum';
import { logger } from '../helpers/logger';


interface SigningJobRequest {
  bucketName: string;
  key: string;
  version?: string;
  profileName: string;
  destinationPrefix: string;
}
export class CodeSigningService {
  private signerClient: SignerClient;
  private s3Client: S3Client;

  constructor() {
		this.signerClient = new SignerClient({
      region: process.env.AWS_REGION || 'us-east-1',
    });
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION || 'us-east-1',
    });
	}

  public async startSigningJob(request: SigningJobRequest): Promise<void> {
    logger.info('Starting signing job', {
      bucket: request.bucketName,
      key: request.key,
      version: request.version,
      profile: request.profileName,
      destinationPrefix: request.destinationPrefix,
    });
    if (!request.version) {
      throw new Error('version is required for StartSigningJobCommand');
    }

    const command = new StartSigningJobCommand({
      source: {
        s3: {
          bucketName: request.bucketName,
          key: request.key,
          version: request.version,
        },
      },
      destination: {
        s3: {
          bucketName: request.bucketName,
          prefix: request.destinationPrefix,
        },
      },
      profileName: request.profileName,
    });

    try {
      const response = await this.signerClient.send(command);

      logger.info('Signing job started successfully', {
        jobId: response.jobId,
        source: `s3://${request.bucketName}/${request.key}`,
        destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
        profile: request.profileName,
      });

      // Start monitoring the job and copy with custom name when complete
      if (response.jobId) {
        this.monitorAndCopySignedFile(response.jobId, request).catch((error: any) => {
          logger.error('Error in post-processing signed file', {
            jobId: response.jobId,
            error: error.message
          });
        });
      }
    } catch (error: any) {
      logger.error('Failed to start signing job', {
        error: error.message,
        source: `s3://${request.bucketName}/${request.key}`,
        profile: request.profileName,
      });
      throw error;
    }
  }

  /**
   * Monitor signing job and copy signed file with custom name when complete
   */
  private async monitorAndCopySignedFile(jobId: string, request: SigningJobRequest): Promise<void> {
    const maxAttempts = 30; // 5 minutes with 10-second intervals
    const pollInterval = 10000; // 10 seconds

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const jobStatus = await this.checkSigningJobStatus(jobId);

        if (jobStatus.status === 'Succeeded' && jobStatus.signedObjectLocation) {
          logger.info('Signing job completed, copying to custom name', {
            jobId,
            signedLocation: jobStatus.signedObjectLocation,
          });

          await this.copySignedFileWithCustomName(jobStatus.signedObjectLocation, request);
          return;
        } else if (jobStatus.status === 'Failed') {
          logger.error('Signing job failed', { jobId, statusReason: jobStatus.statusReason });
          return;
        }

        // Job still in progress, wait before next check
        if (attempt < maxAttempts) {
          await this.delay(pollInterval);
        }
      } catch (error: any) {
        logger.error('Error checking signing job status', {
          jobId,
          attempt,
          error: error.message
        });

        if (attempt < maxAttempts) {
          await this.delay(pollInterval);
        }
      }
    }

    logger.warn('Signing job monitoring timed out', { jobId });
  }

  /**
   * Check the status of a signing job
   */
  private async checkSigningJobStatus(jobId: string): Promise<{
    status: string;
    statusReason?: string;
    signedObjectLocation?: string;
  }> {
    const command = new DescribeSigningJobCommand({ jobId });
    const response = await this.signerClient.send(command);

    return {
      status: response.status || 'Unknown',
      statusReason: response.statusReason,
      signedObjectLocation: response.signedObject?.s3?.key,
    };
  }

  /**
   * Copy signed file from UUID name to custom name
   */
  private async copySignedFileWithCustomName(
    signedObjectKey: string,
    originalRequest: SigningJobRequest
  ): Promise<void> {
    const customFileName = this.generateCustomFileName(originalRequest.key);
    const customKey = `${originalRequest.destinationPrefix}${customFileName}`;

    logger.info('Copying signed file to custom name', {
      from: signedObjectKey,
      to: customKey,
    });

    try {
      // Copy the signed file with custom name
      const copyCommand = new CopyObjectCommand({
        Bucket: originalRequest.bucketName,
        CopySource: `${originalRequest.bucketName}/${signedObjectKey}`,
        Key: customKey,
      });

      await this.s3Client.send(copyCommand);

      // Optionally delete the UUID-named file
      const deleteCommand = new DeleteObjectCommand({
        Bucket: originalRequest.bucketName,
        Key: signedObjectKey,
      });

      await this.s3Client.send(deleteCommand);

      logger.info('Successfully copied and cleaned up signed file', {
        customKey,
        originalKey: signedObjectKey,
      });
    } catch (error: any) {
      logger.error('Error copying signed file to custom name', {
        error: error.message,
        from: signedObjectKey,
        to: customKey,
      });
      throw error;
    }
  }

  /**
   * Generate custom filename for signed file
   */
  private generateCustomFileName(originalKey: string): string {
    const pathParts = originalKey.split('/');
    const fileName = pathParts[pathParts.length - 1];
    const fileNameWithoutExt = fileName.replace(/\.[^/.]+$/, '');
    const extension = fileName.split('.').pop();

    // Add timestamp and "signed" suffix
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    return `${fileNameWithoutExt}_signed_${timestamp}.${extension}`;
  }

  /**
   * Utility method to add delay
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public getSigningProfile(objectKey: string, bucketName: string): string | null {
  // Frm Database we will be importing in future
  for (const [fileType, profile] of Object.entries(codeSigningProfiles)) {
    if (objectKey.includes(fileType)) {
      return profile;
    }
  }
  return null;
}
}