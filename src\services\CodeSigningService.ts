import { SignerClient, StartSigningJobCommand } from '@aws-sdk/client-signer';
import { codeSigningProfiles } from '../enum/CjcFirmawareEnum';
import { logger } from '../helpers/logger';


interface SigningJobRequest {
  bucketName: string;
  key: string;
  version?: string;
  profileName: string;
  destinationPrefix: string;
}
export class CodeSigningService {
  private signerClient: SignerClient;

  constructor() {
		this.signerClient = new SignerClient({
      region: process.env.AWS_REGION || 'us-east-1',
    });
	}

  public async startSigningJob(request: SigningJobRequest): Promise<void> {
    logger.info('Starting signing job', {
      bucket: request.bucketName,
      key: request.key,
      version: request.version,
      profile: request.profileName,
      destinationPrefix: request.destinationPrefix,
    });
    if (!request.version) {
      throw new Error('version is required for StartSigningJobCommand');
    }

    const command = new StartSigningJobCommand({
      source: {
        s3: {
          bucketName: request.bucketName,
          key: request.key,
          version: request.version,
        },
      },
      destination: {
        s3: {
          bucketName: request.bucketName,
          prefix: request.destinationPrefix,
        },
      },
      profileName: request.profileName,
    });

    try {
      const response = await this.signerClient.send(command);

      logger.info('Signing job started successfully', {
        jobId: response.jobId,
        source: `s3://${request.bucketName}/${request.key}`,
        destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
        profile: request.profileName,
      });

      // TODO: Optionally store job information in database for tracking
      // await storeSigningJobInfo(response.jobId, request);
    } catch (error: any) {
      logger.error('Failed to start signing job', {
        error: error.message,
        source: `s3://${request.bucketName}/${request.key}`,
        profile: request.profileName,
      });
      throw error;
    }
  }

  public getSigningProfile(objectKey: string, bucketName: string): string | null {
  // Frm Database we will be importing in future
  for (const [fileType, profile] of Object.entries(codeSigningProfiles)) {
    if (objectKey.includes(fileType)) {
      return profile;
    }
  }
  return null;
}
}