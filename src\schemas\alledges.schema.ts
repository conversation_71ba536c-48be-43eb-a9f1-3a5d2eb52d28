import { Schema, model } from 'mongoose';
import { IAllEdges } from '../models';

const TargetSchema = new Schema(
  {
    targetName: { type: String, required: false, index: true, default: null },
    version: { type: String, required: false, index: true, default: null },
  },
  { _id: false },
);

const CurrentJobSchema = new Schema(
  {
    jobId: { type: String, default: null, index: true },
    jobStatus: { type: String, default: null, index: true },
    completedAt: { type: Date, default: null },
    targets: { type: [TargetSchema], default: [] },
  },
  { _id: false },
);

const AllEdgesSchema = new Schema(
  {
    id: { type: String },
    edgeNodeId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    siteName: { type: String, index: true },
    edgeId: {
      type: String,
      required: true,
      unique: true,
      index: true,
    },
    isOnline: { type: Boolean, default: false, index: true },
    isDeleted: { type: Boolean, default: false, index: true },
    currentJob: {
      type: CurrentJobSchema,
      default: () => ({
        jobId: null,
        jobStatus: null,
        completedAt: null,
        targets: [],
      }),
    },
    targets: {
      type: [TargetSchema],
      default: [],
    },
  },
  {
    timestamps: true,
    collection: 'AllEdges',
  },
);

export const EdgeModel = model<IAllEdges>('AllEdges', AllEdgesSchema);
