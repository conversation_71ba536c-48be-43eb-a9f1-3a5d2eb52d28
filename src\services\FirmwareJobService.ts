import {
  Describe<PERSON><PERSON><PERSON>ommand,
  DescribeJ<PERSON><PERSON>ommandOutput,
  Create<PERSON>ob<PERSON>ommand,
  CreateJobCommandInput,
  CreateJobCommandOutput,
  DescribeThingCommand,
  DescribeThingCommandOutput,
  DescribeJobExecutionCommand,
  DescribeJobExecutionCommandOutput,
  JobStatus as AwsJobStatus,
} from '@aws-sdk/client-iot';

import { EventPayload } from 'models/interfaces/EventPayload';
import { EdgeJobDetails, FirmwareJob, FirmwareJobExecution, TargetVersions } from '../models';
import {
  isValidJobId,
  logger,
  invalidJobIdReponse,
  verifyJobIdAndExtractThingName,
  getFormattedDateTime,
} from '../helpers';
import { JobDocumentBuilder } from '../helpers/jobDocumentBuilder';
import { TargetSelectionType } from '../enum/IotEnum';

// eslint-disable-next-line import/no-internal-modules
import { eventName } from '../enum/eventName.enum';
import { JobStatus, TargetUpdateOperations } from '../enum';
import { awsToCustomJobStatusMap } from '../types';
import { IotService } from './IotService';
import { EventService } from './EventService';
import { EdgeSyncService } from './EdgeSyncService';
import { MongoDBAdapter } from '../adapters';

export class FirmwareJobService {
  private readonly _iotService: IotService;
 
  private mongoDb: MongoDBAdapter;
 
  private readonly _eventService: EventService;
 
  private readonly _jobDocumentBuilder: JobDocumentBuilder;
 
  constructor() {
    this._iotService = IotService.getInstance();
    this._jobDocumentBuilder = new JobDocumentBuilder();
    this._eventService = new EventService();
    this.mongoDb = new MongoDBAdapter();
  }
 
  private async init() {
    await this.mongoDb.connect();
  }
 
  async describeFirmwareJob(jobIds: string[]): Promise<FirmwareJob[]> {
    const firmwareJobDetails = jobIds.map(async (jobId) => {
      try {
        const isJobIdValid = isValidJobId(jobId);
        if (!isJobIdValid) {
          return invalidJobIdReponse(jobId);
        }
        const jobCommand = new DescribeJobCommand({ jobId });
        const response: DescribeJobCommandOutput = await this._iotService.sendIotJob(jobCommand);
        if (!response.job) {
          return invalidJobIdReponse(jobId);
        }
        const { job } = response;
        return {
          jobId,
          completedAt: job?.completedAt?.toISOString(),
          createdAt: job?.createdAt?.toISOString(),
          lastUpdatedAt: job?.lastUpdatedAt?.toISOString(),
          status: job?.status,
          targets: job?.targets,
        };
      } catch (error) {
        logger.error(`Error in describeFirmwareJob for JobId - ${jobId}: ${error}`);
        return invalidJobIdReponse(jobId);
      }
    });
    const firmwareJobDetailsResponse = await Promise.all(firmwareJobDetails);
    logger.info(`Describe FirmwareJob ::: ${JSON.stringify(firmwareJobDetailsResponse)}`);

    return firmwareJobDetailsResponse;
  }

  private async _createJob(
    jobId: string,
    targets: string[],
    jobDocuments: object,
    description: string,
    presignedUrlRoleArn: string,
  ): Promise<CreateJobCommandOutput> {
    const params: CreateJobCommandInput = {
      jobId,
      targets,
      document: JSON.stringify(jobDocuments),
      description,
      targetSelection: TargetSelectionType.SNAPSHOT,
      presignedUrlConfig: {
        roleArn: presignedUrlRoleArn,
      },
    };

    logger.info(`Creating job with params: ${JSON.stringify(params)}`);

    const createIotJobCommand = new CreateJobCommand(params);
    return this._iotService.sendIotJob(createIotJobCommand);
  }

  private mapAwsJobStatusToCustomEnum(awsJobStatus: string | null): JobStatus {
    if (!awsJobStatus) {
      return JobStatus.Queued; // Default fallback
    }

    // Cast string to AWS enum and map to your custom enum
    const awsStatus = awsJobStatus as AwsJobStatus;
    return awsToCustomJobStatusMap[awsStatus] || JobStatus.Queued;
  }

  public async createOtaJobs(
    targetThings: string[],
    targetVersions: TargetVersions[],
    targetUpdateOperation: TargetUpdateOperations,
    userName: string,
    lteModuleUrl = '',
  ): Promise<any> {
    try {
      this.init();
      // validation for job groups need to be added here
      const presignedUrlArn = process.env.S3_ROLE_ARN as string;
      const successfullJobIds: string[] = [];
      const failedJobIdForThings: string[] = [];
      const { mappedThingsArn = {}, invalidThingsId = [] } = await this.getThingsArn(targetThings);
      if (!mappedThingsArn) {
        throw new Error('Invalid things id');
      }

      const jobDocument = await this._jobDocumentBuilder.createJobDocument(
        targetUpdateOperation,
        targetVersions,
        lteModuleUrl,
      );
      if (!jobDocument) {
        throw new Error('Something went wrong during creating job documents');
      }
      const validThingsId = Object.keys(mappedThingsArn);
      for (const thingId of validThingsId) {
        try {
          const edgeId = thingId.split('-')[1];
          // eslint-disable-next-line no-await-in-loop
          // const hasActiveJob = await EdgeSyncService.checkActiveJob(edgeId);

          // if (hasActiveJob) {
          //   throw new Error(
          //     `Job ${thingId} already exists with a queued or in-progress. Please wait for it to complete before creating another.`,
          //   );
          // }

          const thingArn: string = mappedThingsArn[thingId];
          const uniqueJobId = this._createUniuqeJobId(thingId);
          const description = `Update ${thingId} with firmware versions: ${targetVersions
            .map(({ firmwareName, version }) => `${firmwareName} v${version}`)
            .join(', ')}`;

          // eslint-disable-next-line no-await-in-loop
          const createdJobResponse: CreateJobCommandOutput = await this._createJob(
            uniqueJobId,
            [thingArn],
            jobDocument,
            description,
            presignedUrlArn,
          );

          logger.info(`Created job response for thingId - ${JSON.stringify(createdJobResponse)}`);
          if (createdJobResponse?.jobId) {
            const jobId = createdJobResponse?.jobId;
            logger.info(`Job created successfully with ID: ${jobId}`);

            // Get current job status from AWS IoT
            // eslint-disable-next-line no-await-in-loop
            const {currentJobStatus} = await this._iotService.getJobStatus(jobId);

            // Map AWS IoT job status to your JobStatus enum
            const mappedJobStatus = this.mapAwsJobStatusToCustomEnum(currentJobStatus);

            const edgeJobDetails: EdgeJobDetails = {
              currentJob: {
                jobId,
                jobStatus: mappedJobStatus,
                completedAt: null,
                targets: targetVersions.map((tv) => ({
                  targetName: tv.firmwareName,
                  version: tv.version,
                })),
              },
            };

            // eslint-disable-next-line no-await-in-loop
            await EdgeSyncService.updateEdgeDataByEdgeId(edgeId, edgeJobDetails);
            logger.info(`Updated edge data for ${edgeId} with jobId: ${jobId} and status: ${mappedJobStatus}`);

            const eventPayload: EventPayload = {
              edgeSerialNumber: edgeId,
              userName,
              targetUpdateOperation,
              targetVersions,
              eventName: eventName.FIRMWARE_JOB,
            };
            // eslint-disable-next-line no-await-in-loop
            await this._eventService.createNewEvent(eventPayload);
            successfullJobIds.push(createdJobResponse.jobId);
          } else {
            logger.info(`failure occured in else`);
            failedJobIdForThings.push(thingId);
          }
        } catch (error) {
          logger.info(`failure occured in catch`);
          logger.error(`Error occurred during creating job for thingId - ${thingId}`, { error });
          failedJobIdForThings.push(thingId);
        }
      }
      return { successfullJobIds, failedJobIdForThings, invalidThingsId };
    } catch (error) {
      logger.error(`Error occurred during creating job`, { error });
      throw error;
    }
  }

  // eslint-disable-next-line class-methods-use-this
  private _createUniuqeJobId(targetId: string): string {
    return `${targetId}-${getFormattedDateTime()}`;
  }

  private async getThingsArn(thingsName: string[]): Promise<any> {
    const invalidThingsId: string[] = [];
    const mappedThingsArn: any = {};
    for (const item of thingsName) {
      try {
        const describeThingsCommand = new DescribeThingCommand({ thingName: item });
        // eslint-disable-next-line no-await-in-loop
        const response: DescribeThingCommandOutput = await this._iotService.sendIotJob(describeThingsCommand);
        if (response.thingArn) {
          mappedThingsArn[item] = response.thingArn;
        } else {
          invalidThingsId.push(item);
        }
      } catch (error) {
        logger.error(`Error occurred during featching thing arn`, { error });
        invalidThingsId.push(item);
      }
    }
    return { mappedThingsArn, invalidThingsId };
  }

  async describeFirmwareJobExecution(jobIds: string[]): Promise<FirmwareJobExecution[]> {
    const iotService = new IotService();
    const firmwareJobExecutions = jobIds.map(async (jobId) => {
      try {
        const thingName = verifyJobIdAndExtractThingName(jobId);
        if (!thingName) {
          return invalidJobIdReponse(jobId);
        }
        const jobExecutionCommand = new DescribeJobExecutionCommand({ jobId, thingName });
        const response: DescribeJobExecutionCommandOutput = await iotService.sendIotJob(jobExecutionCommand);

        if (!response.execution) {
          return invalidJobIdReponse(jobId);
        }
        const { execution } = response;
        const statusDetails = execution?.statusDetails || {};
        const progress = Object.entries(statusDetails).map(([firmwareName, status]) => ({
          firmwareName,
          status,
        }));

        return {
          jobId,
          executionNumber: execution?.executionNumber,
          lastUpdatedAt: execution?.lastUpdatedAt?.toISOString(),
          queuedAt: execution?.queuedAt?.toISOString(),
          startedAt: execution?.startedAt?.toISOString(),
          status: execution?.status,
          progress,
        };
      } catch (error) {
        logger.error(`Error in describeFirmwareJobExecution for JobId - ${jobId}: ${error}`);
        return invalidJobIdReponse(jobId);
      }
    });

    const firmwareJobExecutionResponse = await Promise.all(firmwareJobExecutions);
    logger.info(`Describe FirmwareJobExecution ::: ${JSON.stringify(firmwareJobExecutionResponse)}`);

    return firmwareJobExecutionResponse;
  }
}
