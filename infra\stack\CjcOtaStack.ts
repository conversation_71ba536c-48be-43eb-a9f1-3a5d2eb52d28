import { resolve } from 'path';

import { Stack as BaseStack, CfnOutput, StackProps } from 'aws-cdk-lib';
import { HttpApi, HttpMethod } from 'aws-cdk-lib/aws-apigatewayv2';
import { HttpIamAuthorizer } from 'aws-cdk-lib/aws-apigatewayv2-authorizers';
import { HttpLambdaIntegration } from 'aws-cdk-lib/aws-apigatewayv2-integrations';
import { IVpc } from 'aws-cdk-lib/aws-ec2';
import { Effect, PolicyStatement, Role, AccountPrincipal, ServicePrincipal } from 'aws-cdk-lib/aws-iam';
import { Runtime, Tracing } from 'aws-cdk-lib/aws-lambda';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import * as ec2 from 'aws-cdk-lib/aws-ec2';
import { NameSpacedStage } from '@zephyr/backend-lib-infrastructure';
import * as s3 from 'aws-cdk-lib/aws-s3';

import { RepositoryName, STAGE_NAME } from '../../src/enum';
import { Helper } from '../Helper';
import { Configuration } from '../Configuration';

/**
 * A CDK stack of the application.
 *
 * Assumptions:
 * - The AWS Route 53 Hosted Zone for the API domain name exists;
 */

export class CjcOtaJobServiceStack extends BaseStack {
  /**
   * The VPC to which the stack is deployed.
   */
  private readonly vpc: IVpc;

  private readonly s3RoleArn: string;

  private uploadNotificationLambda: NodejsFunction;

  /**
   * @inheritdoc
   */

  private buildId(name: string, ...parts: string[]): string {
    const prefix = Helper.buildId(this.config.STAGE, this.config.SERVICE_NAME);
    return Helper.buildId(prefix, ...parts);
  }

  public constructor(private scope: NameSpacedStage, private config: Configuration, id: string, props?: StackProps) {
    super(scope, `${id}`, props);
    this.config = config;
    this.vpc = ec2.Vpc.fromLookup(this, `${id}-vpc`, {
      vpcId: config.VPC_ID,
    });

    const bucketName = `${this.config.CJC_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}`;
    const ahpBucketName = `${this.config.AHP_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}`;
    const cjcBackupFilesBucket = s3.Bucket.fromBucketName(this, 'CjcBackupFilesBucket', bucketName);
    const ahpOTABucket = s3.Bucket.fromBucketName(this, 'ahpOTABucket', ahpBucketName);
    const cjcIotJobS3SigningRole = new Role(this, 'CjcIotJobS3SigningRole', {
      roleName: `AhpCjcIoTS3SigningRole-${this.config.STAGE}`,
      assumedBy: new AccountPrincipal(this.config.CARRIERIO_ACCOUNT_ID),
      description: 'Role for signing S3 URLs for CJC IoT jobs',
    });

    // this.s3RoleArn = cjcIotJobS3SigningRole.roleArn; // As carrier io need to use the assume role
    this.s3RoleArn = process.env.CARRIERIO_TEMP_S3_ROLEARN as string; // This is temporary role

    cjcIotJobS3SigningRole.addToPolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          's3:Get*',
          's3:List*',
          's3:Describe*',
          's3-object-lambda:Get*',
          's3-object-lambda:List*',
          's3:GetObject',
          's3:PutObject',
        ],
        resources: [cjcBackupFilesBucket.bucketArn, ahpOTABucket.bucketArn],
      }),
    );

    // cjcIotJobS3SigningRole.addToPolicy(
    //   new PolicyStatement({
    //     effect: Effect.ALLOW,
    //     actions: ['sts:AssumeRole'],
    //     resources: [this.s3RoleArn],
    //   }),
    // );

    const lambda = this.cjcOtaJobGraphqlLambda();
    this.grantPermissionsToLambda(lambda);
    this.createHttpApi(lambda, id);

    // Create upload notification Lambda and S3 event source
    this.uploadNotificationLambda = this.createUploadNotificationLambda();
    this.grantPermissionsToUploadLambda(this.uploadNotificationLambda);
    this.setupS3EventNotifications(cjcBackupFilesBucket, ahpOTABucket);

    this.registerStackOutputs();
  }

  private registerStackOutputs(): Array<CfnOutput> {
    const serviceUrl = new CfnOutput(this, `${RepositoryName.AhpCjcOtaJob}ApiUrl`, {
      value: `https://${this.config.HVAC_DOMAIN_NAME}/graphql`,
    });
    return [serviceUrl];
  }

  /**
   * Creates a Lambda function to handle S3 upload notifications
   */
  private createUploadNotificationLambda(): NodejsFunction {
    const id = this.buildId('UploadNotificationLambda');
    const functionName = this.scope.nameSpace.apply(id);

    return new NodejsFunction(this, id, {
      runtime: Runtime.NODEJS_20_X,
      timeout: this.config.LAMBDA_TIMEOUT,
      memorySize: 512,
      vpc: this.vpc,
      description: 'Lambda function to handle firmware upload notifications',
      functionName,
      entry: resolve(__dirname, '../../dist/src/lambdas/FirmwareUploadNotificationLambda.js'),
      handler: 'handler',
      tracing: Tracing.ACTIVE,
      environment: {
        STAGE: this.config.STAGE,
        CARRIERIO_ROLE_ARN: this.config.CARRIERIO_ROLE_ARN,
        CJC_OTA_SERVICE_BUCKET_NAME: this.config.CJC_OTA_SERVICE_BUCKET_NAME,
        AHP_OTA_SERVICE_BUCKET_NAME: this.config.AHP_OTA_SERVICE_BUCKET_NAME,
        DB_URL: this.config.DB_URL,
        DB_NAME: this.config.DB_NAME,
        SECRET_DB_ARN: this.config.SECRET_DB_ARN,
        SECRET_DB_KEY: this.config.SECRET_DB_KEY,
        // Add AWS Signer configuration if needed
        AWS_SIGNER_PROFILE_NAME: process.env.AWS_SIGNER_PROFILE_NAME || 'Test_ESP32FirmwareProfile',
      },
    });
  }

  /**
   * Grant permissions to the upload notification Lambda
   */
  private grantPermissionsToUploadLambda(lambda: NodejsFunction): void {
    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: [
          's3:GetObject',
          's3:GetObjectVersion',
          's3:PutObject',
          's3:DeleteObject',
        ],
        effect: Effect.ALLOW,
        resources: [
          `arn:aws:s3:::${this.config.CJC_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}/*`,
          `arn:aws:s3:::${this.config.AHP_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}/*`,
        ],
      }),
    );

    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: [
          'signer:StartSigningJob',
          'signer:DescribeSigningJob',
          'signer:GetSigningProfile',
        ],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );

    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: ['sts:AssumeRole'],
        effect: Effect.ALLOW,
        resources: [this.config.CARRIERIO_ROLE_ARN],
      }),
    );

    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: ['secretsmanager:GetSecretValue'],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );
  }

  /**
   * Setup S3 event notifications for firmware uploads
   */
  private setupS3EventNotifications(cjcBucket: s3.IBucket, ahpBucket: s3.IBucket): void {
    // Grant S3 permission to invoke the Lambda function
    this.uploadNotificationLambda.addPermission('S3InvokePermission', {
      principal: new ServicePrincipal('s3.amazonaws.com'),
      action: 'lambda:InvokeFunction',
      sourceArn: `arn:aws:s3:::${this.config.CJC_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}`,
    });

    this.uploadNotificationLambda.addPermission('S3InvokePermissionAHP', {
      principal: new ServicePrincipal('s3.amazonaws.com'),
      action: 'lambda:InvokeFunction',
      sourceArn: `arn:aws:s3:::${this.config.AHP_OTA_SERVICE_BUCKET_NAME}-${this.config.STAGE}`,
    });

    // Note: Since we're using fromBucketName (existing buckets),
    // S3 event notifications need to be configured manually or via separate CDK construct
    // For now, we'll add the Lambda permissions and document the manual S3 configuration needed

    // Alternative: Use AWS CLI or Console to add S3 event notifications:
    // aws s3api put-bucket-notification-configuration --bucket BUCKET_NAME --notification-configuration file://notification.json
  }

  /**
   * Creates a CDK construct that provides a handler for the HTTP API.
   * @param environment An optional collection of environment variables to be passed into the `process.env` of the
   * lambda's handler.
   */

  private cjcOtaJobGraphqlLambda(): NodejsFunction {
    const id = this.buildId('Lambda');
    const memorySize = this.config.STAGE === STAGE_NAME.LOCAL ? undefined : this.config.LAMBDA_MEMORY_SIZE;
    const timeout = this.config.LAMBDA_TIMEOUT;
    const functionName = this.scope.nameSpace.apply(id);

    return new NodejsFunction(this, id, {
      runtime: Runtime.NODEJS_20_X,
      environment: {
        STAGE: this.config.STAGE,
        CARRIERIO_ROLE_ARN: this.config.CARRIERIO_ROLE_ARN,
        PLATFORM_NAME: this.config.PLATFORM_NAME,
        PLATFORM_TOKEN: this.config.PLATFORM_TOKEN,
        SERVICE_NAME: this.config.SERVICE_NAME,
        HVAC_DOMAIN_NAME: this.config.HVAC_DOMAIN_NAME,
        S3_ROLE_ARN: this.s3RoleArn,
        CJC_OTA_SERVICE_BUCKET_NAME: this.config.CJC_OTA_SERVICE_BUCKET_NAME,
        DB_URL: this.config.DB_URL,
        DB_NAME: this.config.DB_NAME,
        SECRET_DB_ARN: this.config.SECRET_DB_ARN,
        SECRET_DB_KEY: this.config.SECRET_DB_KEY,
        NODE_API_URL: this.config.NODE_API_URL,
        AHP_OTA_SERVICE_BUCKET_NAME: this.config.AHP_OTA_SERVICE_BUCKET_NAME,
      },
      memorySize,
      timeout,
      vpc: this.vpc,
      description: 'A Lambda function providing the GraphQL Server for CJC OTA jobs',
      functionName,
      entry: resolve(__dirname, '../../dist/src/index.js'),
      handler: 'default',
      tracing: Tracing.ACTIVE,
    });
  }

  private grantPermissionsToLambda(lambda: NodejsFunction): void {
    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: ['iam:PassRole', 'sts:AssumeRole'],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );

    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: ['secretsmanager:GetSecretValue'],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );
    lambda.addToRolePolicy(
      new PolicyStatement({
        actions: [
          'iot:DescribeJob',
          'iot:DescribeJobExecution',
          'iot:CreateJob',
          'iot:DescribeThing',
          'iot:ListThings',
        ],
        effect: Effect.ALLOW,
        resources: ['*'],
      }),
    );

    lambda.addToRolePolicy(
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          's3:Get*',
          's3:List*',
          's3:Describe*',
          's3-object-lambda:Get*',
          's3-object-lambda:List*',
          's3:PutObject',
          's3:GetObject',
          's3:ListBucket',
        ],
        resources: [
          `arn:aws:s3:::cjc-backup-files-bucket-${this.config.STAGE}/GatewayDocs/OTA/*`,
          `arn:aws:s3:::cjc-backup-files-bucket-${this.config.STAGE}`,
          `arn:aws:s3:::ahp-gateway-ota-${this.config.STAGE}`,
          `arn:aws:s3:::ahp-gateway-ota-${this.config.STAGE}/*`,
        ],
      }),
    );
  }

  /**
   * Creates a CDK construct that provides the API gateway.
   * @param handler NodeJS Lambda function handing a requests.
   */
  private createHttpApi(handler: NodejsFunction, id: string): HttpApi {
    const httpApi = new HttpApi(this, id, {
      description: 'HTTP API endpoint of the CJC OTA Jobs GraphQL Server',
      apiName: Helper.convertIdToName(id),
      createDefaultStage: true,
      defaultAuthorizer: new HttpIamAuthorizer(),
    });

    this.registerHttpApiHandler(httpApi, handler);

    if (this.config.STAGE !== STAGE_NAME.LOCAL) {
      // eslint-disable-next-line no-new
      new CfnOutput(this, 'HttpApiArnOutput', {
        value: this.formatArn({
          service: 'execute-api',
          resource: httpApi.apiId,
        }),
        exportName: `${RepositoryName.AhpCjcOtaJob}ApiHttpApiArn`,
      });
    }

    return httpApi;
  }

  /**
   * Registers the specified Lambda Function as a handler of the given HTTP API Gateway.
   * @param httpApi The API Gateway.
   * @param handler The Lambda Function.
   */
  private registerHttpApiHandler(httpApi: HttpApi, handler: NodejsFunction): void {
    const id = this.buildId('HttpApi', 'LambdaIntegration');
    const integration = new HttpLambdaIntegration(id, handler);

    httpApi.addRoutes({
      integration,
      path: '/graphql',
      methods: [HttpMethod.GET, HttpMethod.POST, HttpMethod.OPTIONS],
    });
  }
}
