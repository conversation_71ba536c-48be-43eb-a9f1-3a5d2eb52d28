{"name": "@ahp/ahp-service-cjc-ota-job", "version": "0.0.0", "description": "Example project", "license": "Carrier", "author": "Carrier Digital", "main": "dist/index.js", "scripts": {"build": "rm -rf ./dist && tsc", "prebuild": "yarn fetch-env-vars", "check-types": "tsc --noEmit", "lint": "eslint ./infra ./src --ext .ts,.js", "prepare": "husky install", "rc": "./cicd/scripts/rc.sh", "test:integration": "jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.integration.js --passWithNoTests", "test:unit": "jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.unit.js --passWithNoTests", "test": "jest --coverage --collectCoverageFrom='./src/**' --config ./jest.config.integration.js", "watch": "tsc -w", "start": "yarn build && tsc-watch -p ./tsconfig.json --onSuccess \"sls offline --stage local\"", "infra": "run-s infra:*", "infra:build": "yarn build && cdk synth --context stage=qa --all", "infra:deploy": "cdk deploy --force --context stage=qa qa/*", "infra:list": "cdk ls --context stage=qa", "fetch-env-vars": "sh cicd/scripts/fetch-env-vars.sh", "offline": "serverless offline"}, "lint-staged": {"**/*.{json,yml,yaml}": ["prettier --write"], "**/*.{js,ts}": ["eslint --ext .js,.ts --cache --fix"]}, "dependencies": {"@apollo/subgraph": "^2.9.2", "@aws-sdk/client-iot": "^3.731.1", "@aws-sdk/client-iot-jobs-data-plane": "^3.864.0", "@aws-sdk/client-secrets-manager": "^3.658.1", "@aws-sdk/client-signer": "^3.731.1", "@aws-sdk/client-sts": "^3.731.1", "@carrier-io/backend-lib-logger": "^1.0.1", "@carrier-io/backend-lib-node-sdk": "^2.72.0", "@carrier-io/io-lib-node-sdk-models": "^1.11.0", "@carrier/backend-lib-core": "^3.0.2", "@graphql-tools/utils": "^10.7.2", "@types/jsonwebtoken": "^9.0.8", "@zephyr/backend-lib-infrastructure": "^3.0.4", "apollo-graphql": "^0.9.7", "apollo-server-lambda": "^3.13.0", "aws-xray-sdk-core": "^3.10.1", "class-validator": "^0.14.1", "dotenv": "^16.4.5", "graphql": "^16.8.1", "graphql-scalars": "^1.23.0", "graphql-tag": "^2.12.6", "graphql-type-json": "^0.3.2", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mongoose": "^8.10.0", "reflect-metadata": "^0.2.2", "type-graphql": "^2.0.0-rc.2", "typescript-optional": "^3.0.0-alpha.3"}, "devDependencies": {"@aws-sdk/client-ssm": "^3.468.0", "@tsconfig/node20": "^20.1.0", "@types/jest": "^27.0.2", "@types/node": "^20.10.0", "@types/source-map-support": "^0.5.10", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/parser": "^5.41.0", "aws-cdk": "^2.88.0", "aws-cdk-lib": "^2.88.0", "aws-sdk": "^2.1514.0", "constructs": "^10.0.0", "esbuild": "^0.24.0", "eslint": "^8.11.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^26.1.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-sonarjs": "^0.12.0", "husky": "^8.0.0", "inquirer": "^8.2.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-sonar-reporter": "^2.0.0", "lint-staged": "13.1.0", "mongodb-memory-server": "^9.2.0", "npm-run-all": "^4.1.5", "prettier": "^2.6.0", "serverless": "^3.22.0", "serverless-dotenv-plugin": "^4.0.2", "serverless-offline": "^13.8.3", "serverless-plugin-typescript": "^2.1.5", "source-map-support": "^0.5.20", "ts-essentials": "^9.2.0", "ts-jest": "^29.1.2", "ts-jest-resolver": "^2.0.0", "ts-node": "^10.9.2", "tsc-watch": "^5.0.3", "typescript": "^5.4.5"}, "bin": {}, "repository": "*****************:carrier-digital/ahp-service-cjc-ota-job.git"}