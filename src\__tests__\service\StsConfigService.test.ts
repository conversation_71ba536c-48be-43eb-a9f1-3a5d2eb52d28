import { STSClient } from '@aws-sdk/client-sts';
import { StsConfigService } from '../../services/StsConfigService';
import { logger } from '../../helpers/logger';

jest.mock('@aws-sdk/client-sts');
jest.mock('../../helpers/logger');

describe('StsConfigService', () => {
  let stsConfigService: StsConfigService;
  const mockSend = jest.fn();
  const mockLoggerInfo = jest.fn();
  const mockLoggerError = jest.fn();

  beforeEach(() => {
    jest.resetAllMocks();
    stsConfigService = new StsConfigService();
    mockSend.mockClear();
    mockLoggerInfo.mockClear();
    mockLoggerError.mockClear();
    (STSClient as jest.Mock).mockImplementation(() => ({
      send: mockSend,
    }));
    (logger.info as jest.Mock).mockImplementation(mockLoggerInfo);
    (logger.error as jest.Mock).mockImplementation(mockLoggerError);
  });

  const mockCredentials: any = {
    accessKeyId: 'testAccessKey',
    secretAccessKey: 'testSecretAccessKey',
    sessionToken: 'testSessionToken',
    expiration: new Date(Date.now() + 3600 * 1000),
    region: 'us-east-1',
  };

  const expiredCredentials = {
    accessKeyId: 'expiredAccessKey',
    secretAccessKey: 'expiredSecretAccessKey',
    sessionToken: 'expiredSessionToken',
    expiration: new Date(Date.now() - 3600 * 1000),
    region: 'us-east-1',
  };

  it('should successfully create STS credentials', async () => {
    const spyCreateSTSCredentials = jest
      .spyOn(stsConfigService, 'createSTSCredentials')
      .mockResolvedValue(mockCredentials);

    const credentials = await stsConfigService.getSTSCredentials();

    expect(spyCreateSTSCredentials).toHaveBeenCalledWith('dev');
    expect(credentials).toEqual({
      accessKeyId: 'testAccessKey',
      secretAccessKey: 'testSecretAccessKey',
      sessionToken: 'testSessionToken',
      expiration: expect.any(Date),
      region: 'us-east-1',
    });

    expect(mockSend).toHaveBeenCalledTimes(0);
    expect(mockLoggerError).not.toHaveBeenCalled();
  });

  it('should refresh STS credentials if expired', async () => {
    (stsConfigService as any).assumeCredentials = expiredCredentials;
    const spyCreateSTSCredentials = jest
      .spyOn(stsConfigService, 'createSTSCredentials')
      .mockResolvedValue(expiredCredentials);

    await stsConfigService.getSTSCredentials();
    expect(spyCreateSTSCredentials).toHaveBeenCalledWith('dev');
  });

  it('should get new STS credentials if no cached credentials', async () => {
    (stsConfigService as any).assumeCredentials = null;

    const spyCreateSTSCredentials = jest
      .spyOn(stsConfigService, 'createSTSCredentials')
      .mockResolvedValue(mockCredentials);

    const credentials = await stsConfigService.getSTSCredentials();

    expect(spyCreateSTSCredentials).toHaveBeenCalledWith('dev');
    expect(credentials).toEqual(mockCredentials);

    expect(mockSend).toHaveBeenCalledTimes(0);
    expect(mockLoggerInfo).toHaveBeenCalledWith('No cached credentials found, fetching new ones...');
  });
});
