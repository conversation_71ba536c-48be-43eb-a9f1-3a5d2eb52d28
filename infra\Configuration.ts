import { hostname } from 'os';

import { Duration } from 'aws-cdk-lib';

import { RepositoryName, STAGE_NAME } from '../src/enum';

/**
 * Configuration of the infrastructure.
 */
export class Configuration {
  /**
   * Configuration of the infrastructure.
   */
  /**
   * Unique name of application that will be used in AWS Cloud Formation.
   */

  public readonly CARRIERIO_ROLE_ARN = process.env.CARRIERIO_ROLE_ARN ?? '';

  /**
   * Name of the platform.
   */
  public readonly PLATFORM_NAME = process.env.PLATFORM_NAME as string;

  public readonly PLATFORM_TOKEN = process.env.PLATFORM_TOKEN as string;

  /**
   * Stage name
   */

  /**
   * The stage name to which this application will be deployed.
   */
  public readonly STAGE = process.env.STAGE ?? '';

  public readonly NODE_API_URL = process.env.NODE_API_URL as string;

  /**
   * Indicates that the application is building for the local deployment.
   */
  public readonly IS_LOCAL = !process.env.STAGE;

  /**
   * Indicates that the application is building for the production stage.
   */
  public readonly IS_PROD = this.STAGE === STAGE_NAME.PROD;

  public readonly HVAC_DOMAIN_NAME = process.env.HVAC_DOMAIN_NAME ?? 'api.dev.hvac.abound.carrier.io';

  public readonly SERVICE_NAME = RepositoryName.AhpCjcOtaJob;
  /**
   * Lambda common settings
   *
   */

  /**
   * The maximal allowed execution time of a lambda.
   */
  public readonly LAMBDA_TIMEOUT = Duration.seconds(60);

  /**
   * The maximal allowed taken memory size of a lambda (in MiB).
   */
  public readonly LAMBDA_MEMORY_SIZE = 512;

  /**
   * Vpc ID
   */

  public readonly VPC_ID = process.env.VPC_ID as string;

  public readonly CARRIERIO_ACCOUNT_ID = process.env.CARRIERIO_ACCOUNT_ID as string;

  // S3 Bucket
  public readonly CJC_OTA_SERVICE_BUCKET_NAME = process.env.CJC_OTA_SERVICE_BUCKET_NAME ?? 'cjc-backup-files-bucket';

  public readonly AHP_OTA_SERVICE_BUCKET_NAME = process.env.AHP_OTA_SERVICE_BUCKET_NAME ?? 'ahp-gateway-ota';

  /**
   * AWS account
   */
  public readonly AWS_ACCOUNT = process.env.CDK_DEFAULT_ACCOUNT || '';

  public readonly AWS_REGION = process.env.CDK_DEFAULT_REGION || '';

  /**
   * OOB DocumentDB creds
   */
  public readonly DB_URL = process.env.DB_URL as string;

  public readonly DB_NAME = process.env.DB_NAME as string;

  public readonly SECRET_DB_KEY = process.env.SECRET_DB_KEY as string;

  public readonly SECRET_DB_ARN = process.env.SECRET_DB_ARN as string;

  public readonly EDGE_MODELS = process.env.EDGE_MODELS as string;

  public readonly BATCH_SIZE = process.env.BATCH_SIZE as string;

  public readonly MAX_RETRIES = process.env.MAX_RETRIES as string;

  public readonly INCLUDE_ASSETS = process.env.INCLUDE_ASSETS as string;

  public readonly OTA_STATUS_UPDATE_QUEUE = process.env.OTA_IOT_TARGET_SQS_NAME || 'ota_edge_job_status_update_queue';

  /**
   * Creates an instance of the configuration.
   */
  public constructor(stageFromContext: string) {
    this.STAGE = stageFromContext ?? (process.env.STAGE || hostname()).toLowerCase().replace(/[\W_]/g, '');
  }
}

// vim:expandtab:sw=2:ts=2
