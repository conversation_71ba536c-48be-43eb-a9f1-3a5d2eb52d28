import { TargetDirectories, TargetFirmwareNames, TargetUpdateOperations } from '../../enum';

export const FirmwareToDirectoryMap: Record<TargetFirmwareNames, TargetDirectories> = {
  [TargetFirmwareNames.ESP32_firmware]: TargetDirectories.ESP32_firmware,
  [TargetFirmwareNames.ESP32_filesystem]: TargetDirectories.ESP32_filesystem,
  [TargetFirmwareNames.Claim_cert]: TargetDirectories.Claim,
  [TargetFirmwareNames.Claim_private]: TargetDirectories.Claim,
  [TargetFirmwareNames.LTEmodule]: TargetDirectories.LTEmodule,
  [TargetFirmwareNames.AmazonRootCA1]: TargetDirectories.AmazonRootCA1,
  [TargetFirmwareNames.RX651_CHL]: TargetDirectories.RX651_CHL,
};

export const TargetBulkOperationGroups: Partial<{ [key in TargetUpdateOperations]: TargetFirmwareNames[] }> = {
  [TargetUpdateOperations.BulksAll]: [
    TargetFirmwareNames.ESP32_firmware,
    TargetFirmwareNames.ESP32_filesystem,
    TargetFirmwareNames.RX651_CHL,
  ],

  [TargetUpdateOperations.BulksESP32]: [TargetFirmwareNames.ESP32_firmware, TargetFirmwareNames.ESP32_filesystem],
  [TargetUpdateOperations.Claim]: [TargetFirmwareNames.Claim_cert, TargetFirmwareNames.Claim_private],
  [TargetUpdateOperations.LTEmodule]: [TargetFirmwareNames.LTEmodule],
  [TargetUpdateOperations.AmazonRootCA1]: [TargetFirmwareNames.AmazonRootCA1],
  [TargetUpdateOperations.RX651_CHL]: [TargetFirmwareNames.RX651_CHL],
};

export interface Input {
  handler: string;
  args: any[];
}

export interface LTEModuleFileInfo {
  url: string;
  moduleName?: string;
}

export interface FileInfo extends LTEModuleFileInfo {
  filename: string;
  version: string;
  priority?: number;
  firmwareName?: string;
}

export interface JobDocument {
  version: string;
  steps: Array<{
    action: {
      name: string;
      type: string;
      input: {
        handler: string;
        args: Array<{ [key: string]: { filename?: string; url: string; version?: string; priority?: number } }>;
      };
    };
  }>;
}

interface FileMapping {
  [key: string]: string;
}

export const BASE_S3_OTA_FILE_PATH = 'GatewayDocs/OTA/';

export const BASE_S3_CODE_SIGNING_PATH = `${BASE_S3_OTA_FILE_PATH}signed/`;

export const IotCoreJobType = 'runHandler';

export const IotCoreJobHandlerName = 'CJC-EYE_Handler';

export const FileAndJobNameMapper: FileMapping = {
  'claim_cert.crt': 'claimcertificatefile',
  'claim_private.key': 'claimprivatekeyfile',
  LTEmodule: 'ltefw',
  'ESP32_filesystem.bin': 'esp32file',
  'ESP32_firmware.bin': 'esp32fw',
  'RX651_CHL.mot': 'fieldbusfw',
  'AmazonRootCA1.crt': 'awsrootCAfile',
};

export const AhpFirmwareNameAndTypeMapper: FileMapping = {
  Edge_Adapter: 'Edge_Adapter.tar',
  CCN_Adapter: 'CCN_Adapter.tar',
};

export type FileInfoFetcher = (version: string, lteModuleUrl?: string) => FileInfo[];

export interface checkSigningJobStatus {
     status: string;
     statusReason?: string;
     signedObjectLocation?: string;
   }
