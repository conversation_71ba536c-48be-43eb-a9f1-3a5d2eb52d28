import {
  DescribeJobCommandOutput,
  DescribeJobExecutionCommandOutput,
  IoTClient,
  CreateJobCommandOutput,
  DescribeThingCommandOutput,
  ListThingsCommandOutput,
  GetJobDocumentCommandOutput,
  DescribeJobCommand,
  GetJobDocumentCommandInput,
  GetJobDocumentCommand
} from '@aws-sdk/client-iot';

import { logger } from '../helpers';

// eslint-disable-next-line import/no-internal-modules
import { StsConfigService } from './StsConfigService';
import { JobDocumentBuilder } from '../helpers/jobDocumentBuilder';

export class IotService {
  private static instance: IotService | null = null;

  private iotClient: IoTClient | null = null;

  private credentialsExpiration: Date | undefined = undefined;
  private _jobDocumentBuilder : JobDocumentBuilder

  constructor(){
      this._jobDocumentBuilder = new JobDocumentBuilder();
  }

  public static getInstance(): IotService {
    if (!IotService.instance) {
      IotService.instance = new IotService();
    }
    return IotService.instance;
  }

  private async getIotClient(): Promise<IoTClient> {
    if (this.iotClient && this.credentialsExpiration && this.credentialsExpiration.getTime() - Date.now() > 300000) {
      return this.iotClient;
    }

    const stsObj = new StsConfigService();
    const { accessKeyId, secretAccessKey, sessionToken, region, expiration }: any = await stsObj.getSTSCredentials();
    this.iotClient = new IoTClient({
      credentials: { accessKeyId, secretAccessKey, sessionToken },
      region,
    });
    this.credentialsExpiration = expiration;

    return this.iotClient;
  }


  async getJobStatusAndTargetVersions(jobId : string) : Promise<any | null>{
    try {
      const describeJobCommand = new GetJobDocumentCommand({
        jobId
      });
      const response = await this.sendIotJob(describeJobCommand);
      // Type assertion since we know it's DescribeJobCommandOutput
      const jobDocumentResponse = response as GetJobDocumentCommandOutput;
      logger.info("jobResponse", `${JSON.stringify(jobDocumentResponse)}`)
      if(jobDocumentResponse.document)
      return this._jobDocumentBuilder.getTargetVersionFromJobDocument(jobDocumentResponse.document)
      return null
       
    } catch (error) {
      logger.error(`Error getting job status for jobId ${jobId}:`, { error });
      return null;
    }
  }
  // Add this method to your existing IotService class
  async getJobStatus(jobId: string): Promise<any | null> {
    try {
      const describeJobCommand = new DescribeJobCommand({
        jobId,
      });
      const response = await this.sendIotJob(describeJobCommand);
      // Type assertion since we know it's DescribeJobCommandOutput
      const jobResponse = response as DescribeJobCommandOutput

      return {
        jobStatus : jobResponse.job?.status
      } 
    } catch (error) {
      logger.error(`Error getting job status for jobId ${jobId}:`, { error });
      return null;
    }
  }

  public async sendIotJob(
    jobCommand: any,
  ): Promise<
    | DescribeJobCommandOutput
    | DescribeJobExecutionCommandOutput
    | CreateJobCommandOutput
    | DescribeThingCommandOutput
    | DescribeJobExecutionCommandOutput
    | ListThingsCommandOutput
    | GetJobDocumentCommandOutput
    
  > {
    try {
      const iotClient = await this.getIotClient();
      // eslint-disable-next-line sonarjs/prefer-immediate-return
      const response = await iotClient.send(jobCommand);
      return response;
    } catch (error) {
      logger.error(`Error in IoT JobCommand: ${error}`);
      throw error;
    }
  }
}
