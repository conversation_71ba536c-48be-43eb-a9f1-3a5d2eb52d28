import {
  Describe<PERSON><PERSON><PERSON><PERSON><PERSON>,
  DescribeJ<PERSON><PERSON>ommandOutput,
  Create<PERSON><PERSON><PERSON>ommand,
  CreateJobCommandInput,
  CreateJobCommandOutput,
  DescribeThingCommand,
  DescribeThingCommandOutput,
} from '@aws-sdk/client-iot';

import { FirmwareJob, AhpTargetVersions, AhpFirmwareNameAndTypeMapper } from '../models';
import { isValidJobId, logger, invalidJobIdReponse, getFormattedDateTime } from '../helpers';
import { TargetSelectionType } from '../enum/IotEnum';

import { IotService } from './IotService';
import { EventService } from './EventService';
import { S3Service } from './S3Service';

export interface AhpJobInput {
  gatewayType: string;
  firmwareType: string;
  version: string;
  targetThings: string[];
  userName: string;
}

export interface AhpJobDocument {
  opType: string;
  url: string;
  module: string;
  version: string;
}

export interface AhpJobResult {
  successfulJobIds: string[];
  failedJobIdForThings: string[];
  invalidThingsId: string[];
}

const AHP_OTA_BUCKET = `${process.env.AHP_OTA_SERVICE_BUCKET_NAME}-${process.env.STAGE}`;

export class AhpJobService {
  private readonly _iotService: IotService;

  private readonly _eventService: EventService;

  private readonly _s3Service: S3Service;

  constructor() {
    this._iotService = IotService.getInstance();
    this._eventService = new EventService();
    this._s3Service = new S3Service(AHP_OTA_BUCKET);
  }

  /**
   * Create AHP job document
   */
  private async _createAhpJobDocument(firmwareType: string, version: string): Promise<AhpJobDocument> {
    try {
      // Generate filename based on firmware type (you may need to adjust this based on your naming convention)
      const fileName = AhpFirmwareNameAndTypeMapper[firmwareType] || null;

      if (!fileName) {
        throw new Error(`File mapping not found for ${firmwareType}`);
      }

      const filePath = `${firmwareType}/${version}/${fileName}`;
      // Check if file exists in S3
      const fileExists = await this._s3Service.checkFileExistInS3(filePath);

      if (!fileExists) {
        throw new Error(`Firmware file not found in S3 for ${firmwareType}/${version}`);
      }

      // Generate presigned URL
      const fileUrl = await this._s3Service.generatePresignedUrl(filePath, 'getObject');
      logger.info(`fileURL :: ${fileUrl}`);

      // Create job document
      const jobDocument: AhpJobDocument = {
        url: fileUrl,
        opType: 'firmware_update',
        module: firmwareType.split('_')[0].toLocaleLowerCase(),
        version: version
      };

      logger.info(`Created AHP job document for $${firmwareType}/${version}`);
      return jobDocument;
    } catch (error: any) {
      logger.error(`Error creating AHP job document: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get filename based on firmware type
   */
  private _getFilenameForFirmwareType(firmwareType: string): string {
    // Generate AHP filename based on firmware type using new pattern
    switch (firmwareType) {
      case 'Edge_Adapter':
        return 'Edge_Adapter.tar';
      case 'CCN_Adapter':
        return 'CCN_Adapter.tar';
      default:
        return '';
    }
  }

  /**
   * Create IoT job
   */
  private async _createJob(
    jobId: string,
    targets: string[],
    jobDocument: AhpJobDocument,
    description: string,
    presignedUrlRoleArn: string,
  ): Promise<CreateJobCommandOutput> {
    const params: CreateJobCommandInput = {
      jobId,
      targets,
      document: JSON.stringify(jobDocument),
      description,
      targetSelection: TargetSelectionType.SNAPSHOT,
      presignedUrlConfig: {
        roleArn: presignedUrlRoleArn,
      },
    };

    const createIotJobCommand = new CreateJobCommand(params);
    return this._iotService.sendIotJob(createIotJobCommand);
  }

  /**
   * Create unique job ID
   */
  private _createUniqueJobId(targetId: string): string {
    return `${targetId}-${getFormattedDateTime()}`;
  }

  /**
   * Get things ARN from thing names
   */
  private async _getThingsArn(thingsName: string[]): Promise<any> {
    const invalidThingsId: string[] = [];
    const mappedThingsArn: any = {};

    for (const item of thingsName) {
      try {
        const describeThingsCommand = new DescribeThingCommand({ thingName: item });
        // eslint-disable-next-line no-await-in-loop
        const response: DescribeThingCommandOutput = await this._iotService.sendIotJob(describeThingsCommand);

        if (response.thingArn) {
          mappedThingsArn[item] = response.thingArn;
        } else {
          invalidThingsId.push(item);
        }
      } catch (error) {
        logger.error(`Error occurred during fetching thing arn`, { error });
        invalidThingsId.push(item);
      }
    }

    return { mappedThingsArn, invalidThingsId };
  }

  /**
   * Create AHP OTA jobs
   */
  public async createAhpOtaJobs(
    targetThings: string[],
    targetVersions: AhpTargetVersions,
    userName = '',
  ): Promise<AhpJobResult> {
    try {
      const presignedUrlArn = process.env.S3_ROLE_ARN as string;
      const successfulJobIds: string[] = [];
      const failedJobIdForThings: string[] = [];

      // Get things ARN
      const { mappedThingsArn = {}, invalidThingsId = [] } = await this._getThingsArn(targetThings);

      if (!mappedThingsArn || Object.keys(mappedThingsArn).length === 0) {
        throw new Error('No valid things found');
      }

      // Create job document
      const jobDocument = await this._createAhpJobDocument(targetVersions.firmwareName, targetVersions.version);

      logger.info(`AHP Job document: ${JSON.stringify(jobDocument)}`);
      if (!jobDocument) {
        throw new Error('Failed to create job document');
      }

      // Create jobs for each valid thing
      const validThingsId = Object.keys(mappedThingsArn);

      for (const thingId of validThingsId) {
        try {
          const thingArn = mappedThingsArn[thingId];
          const uniqueJobId = this._createUniqueJobId(thingId);
          const description = `Update ${thingId} with ${targetVersions.firmwareName} version ${targetVersions.version}`;
          // eslint-disable-next-line no-await-in-loop
          const createdJobResponse: CreateJobCommandOutput = await this._createJob(
            uniqueJobId,
            [thingArn],
            jobDocument,
            description,
            presignedUrlArn,
          );

          if (createdJobResponse?.jobId) {
            // const edgeSerialNumber = thingId.split('-')[1] || thingId;
            // const eventPayload: EventPayload = {
            //     edgeSerialNumber,
            //     userName,
            //     targetUpdateOperation: targetVersions.firmwareName,
            //     targetVersions: [{ firmwareName: targetVersions.firmwareName, version: targetVersions.version }],
            //     eventName: eventName.FIRMWARE_JOB,
            // };

            // eslint-disable-next-line no-await-in-loop
            // await this._eventService.createNewEvent(eventPayload);
            successfulJobIds.push(createdJobResponse.jobId);
          } else {
            failedJobIdForThings.push(thingId);
          }
        } catch (error) {
          logger.error(`Error creating job for thing ${thingId}:`, { error });
          failedJobIdForThings.push(thingId);
        }
      }

      return { successfulJobIds, failedJobIdForThings, invalidThingsId };
    } catch (error) {
      logger.error(`Error occurred during creating AHP jobs`, { error });
      throw error;
    }
  }

  /**
   * Describe AHP firmware job
   */
  public async describeAhpFirmwareJob(jobIds: string[]): Promise<FirmwareJob[]> {
    const firmwareJobDetails = jobIds.map(async (jobId) => {
      try {
        const isJobIdValid = isValidJobId(jobId);
        if (!isJobIdValid) {
          return invalidJobIdReponse(jobId);
        }

        const jobCommand = new DescribeJobCommand({ jobId });
        const response: DescribeJobCommandOutput = await this._iotService.sendIotJob(jobCommand);

        if (!response.job) {
          return invalidJobIdReponse(jobId);
        }

        const { job } = response;
        return {
          jobId,
          completedAt: job?.completedAt?.toISOString(),
          createdAt: job?.createdAt?.toISOString(),
          lastUpdatedAt: job?.lastUpdatedAt?.toISOString(),
          status: job?.status,
          targets: job?.targets,
        };
      } catch (error) {
        logger.error(`Error in describeAhpFirmwareJob for JobId - ${jobId}: ${error}`);
        return invalidJobIdReponse(jobId);
      }
    });

    const firmwareJobDetailsResponse = await Promise.all(firmwareJobDetails);
    logger.info(`Describe AHP FirmwareJob ::: ${JSON.stringify(firmwareJobDetailsResponse)}`);

    return firmwareJobDetailsResponse;
  }
}
