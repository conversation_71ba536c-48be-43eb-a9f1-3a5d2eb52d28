import { BaseService, SelectParams } from '@carrier-io/backend-lib-node-sdk';
import { logger } from '../helpers';
import { AssetEntity, EdgeEntity } from '../models';

export class EdgeListingService {
  private edgeService: BaseService<EdgeEntity>;

  private assetService: BaseService<AssetEntity>;

  constructor() {
    this.edgeService = new BaseService<EdgeEntity>(EdgeEntity);
    this.assetService = new BaseService<AssetEntity>(AssetEntity);
  }

  public async getEdgesBasedOnModel(
    offset: number,
    limit: number,
    params: SelectParams<EdgeEntity>,
  ): Promise<EdgeEntity[]> {
    try {
      const edges = await this.edgeService.select(params);
      const filteredEdges = edges.filter((edge) => edge.isDeleted !== true && edge.isEdgeReplaced !== true);
      logger.info(`Edges found::: ${JSON.stringify(filteredEdges)}`);
      return filteredEdges;
    } catch (error: any) {
      logger.error(`Error fetching edges batch (offset: ${offset}, limit: ${limit})`, error);
      throw error;
    }
  }

  public async getAssetLocationDetailsById(assetId: string): Promise<AssetEntity> {
    const assetSelectParams = this.buildSelectAssetByIdParams(assetId);
    const assetData = await this.assetService.select(assetSelectParams);
    logger.info(`AssetLocationData::: ${JSON.stringify(assetData)}`);
    return assetData[0];
  }

  private buildSelectAssetByIdParams = (assetId: string): SelectParams<AssetEntity> => {
    return {
      filters: { id: { equalTo: assetId } },
      include: ['location'],
    };
  };
}
