import { registerEnumType } from 'type-graphql';

export enum FIRMWAREJOBSTATUS {
  JOBIDNOTFOUND = 'JOB_ID_NOT_FOUND',
}

export enum Connectivity {
  Online = 'Online',
  Offline = 'Offline',
}

export enum JobStatus {
  Success = 'Success',
  Failed = 'Failed',
  InProgress = 'InProgress',
  Queued = 'Queued',
  Canceled = 'Canceled',
  TimedOut = 'TimedOut',
  Rejected = 'Rejected',
  Removed = 'Removed',
}

export enum JobExecutionStatus {
  QUEUED = 'Queued',
  SUCCEEDED = 'Success',
  FAILED = 'Failed',
  REJECTED = 'Rejected',
  TIMED_OUT = 'TimedOut',
  REMOVED = 'Removed',
  IN_PROGRESS = 'InProgress',
  CANCELED = 'Canceled',
}

export enum TargetFirmwarePriorities {
  ESP32_firmware = 3,
  ESP32_filesystem = 2,
  RX651_CHL = 1,
}

export interface PrefixesResult {
  prefixes: string[];
  nextToken?: string;
}

export enum TargetDirectories {
  ESP32_firmware = 'ESP32_firmware',
  ESP32_filesystem = 'ESP32_filesystem',
  Claim = 'claim',
  LTEmodule = 'LTEmodule',
  AmazonRootCA1 = 'AmazonRootCA1',
  RX651_CHL = 'RX651_CHL',
}

export enum TargetFirmwareNames {
  ESP32_firmware = 'ESP32_firmware',
  ESP32_filesystem = 'ESP32_filesystem',
  Claim_cert = 'Claim_cert',
  Claim_private = 'Claim_private',
  LTEmodule = 'LTEmodule',
  AmazonRootCA1 = 'AmazonRootCA1',
  RX651_CHL = 'RX651_CHL',
}

export enum TargetUpdateOperations {
  BulksAll = 'bulksAll',
  BulksESP32 = 'bulksESP32',
  Claim = 'Claim',
  LTEmodule = 'LTEmodule',
  AmazonRootCA1 = 'AmazonRootCA1',
  RX651_CHL = 'RX651_CHL',
}

export enum TargetFirmwareWithExtentions {
  ESP32_firmware = 'ESP32_firmware.bin',
  ESP32_filesystem = 'ESP32_filesystem.bin',
  Claim_cert = 'claim_cert.crt',
  Claim_private = 'claim_private.key',
  AmazonRootCA1 = 'AmazonRootCA1.crt',
  RX651_CHL = 'RX651_CHL.mot',
}

registerEnumType(TargetUpdateOperations, {
  name: 'TargetUpdateOperations',
  description: 'Target operation names for firmware updates',
});

registerEnumType(TargetDirectories, {
  name: 'TargetDirectories',
  description: 'The target directories for firmware upload',
});

registerEnumType(TargetFirmwareNames, {
  name: 'TargetFirmwareNames',
  description: 'Target names for job.',
});

registerEnumType(JobStatus, {
  name: 'JobStatus',
  description: 'OTA job execution status',
});

registerEnumType(Connectivity, {
  name: 'Connectivity',
  description: 'Gateway connectivity status',
});
