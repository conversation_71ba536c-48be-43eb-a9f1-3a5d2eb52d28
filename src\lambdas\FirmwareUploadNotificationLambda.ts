import { S3Event, S3EventRecord } from 'aws-lambda';
import { SignerClient, StartSigningJobCommand } from '@aws-sdk/client-signer';
import { logger } from '../helpers/logger';

interface SigningJobRequest {
  bucketName: string;
  key: string;
  version?: string;
  profileName: string;
  destinationPrefix: string;
}

export const handler = async (event: S3Event): Promise<void> => {
  logger.info('Firmware upload notification received', { 
    recordCount: event.Records.length 
  });

  for (const record of event.Records) {
    try {
      await processFirmwareUpload(record);
    } catch (error: any) {
      logger.error('Error processing firmware upload notification', {
        error: error.message,
        record: record.s3,
      });
      // Continue processing other records even if one fails
    }
  }
};

async function processFirmwareUpload(record: S3EventRecord): Promise<void> {
  const { bucket, object } = record.s3;
  const bucketName = bucket.name;
  const objectKey = decodeURIComponent(object.key.replace(/\+/g, ' '));
  const objectVersion = object.versionId;

  logger.info('Processing firmware upload', {
    bucket: bucketName,
    key: objectKey,
    version: objectVersion,
    eventName: record.eventName,
  });

  // Check if this is a firmware file that needs signing
  if (!shouldSignFile(objectKey)) {
    logger.info('File does not require signing, skipping', { key: objectKey });
    return;
  }

  // Determine signing profile based on file type and bucket
  const signingProfile = getSigningProfile(objectKey, bucketName);
  if (!signingProfile) {
    logger.warn('No signing profile found for file', { key: objectKey, bucket: bucketName });
    return;
  }

  // Start AWS Signer job
  await startSigningJob({
    bucketName,
    key: objectKey,
    version: objectVersion,
    profileName: signingProfile,
    destinationPrefix: 'signed/',
  });
}

function shouldSignFile(objectKey: string): boolean {
  // Define which files should be signed
  const signingPatterns = [
    /\.bin$/i,           // Binary firmware files
    /ESP32_firmware/i,   // ESP32 firmware specifically
    /ESP32_filesystem/i, // ESP32 filesystem
    /RX651_CHL\.mot$/i,  // RX651 firmware
  ];

  // Skip already signed files
  if (objectKey.includes('/signed/')) {
    return false;
  }

  // Skip test files
  if (objectKey.includes('/test/') || objectKey.includes('/testsigning/')) {
    return false;
  }

  return signingPatterns.some(pattern => pattern.test(objectKey));
}

function getSigningProfile(objectKey: string, bucketName: string): string | null {
  // Map file types to signing profiles
  const profileMappings: Record<string, string> = {
    'ESP32_firmware': process.env.AWS_SIGNER_PROFILE_NAME || 'Test_ESP32FirmwareProfile',
    'ESP32_filesystem': process.env.AWS_SIGNER_PROFILE_NAME || 'Test_ESP32FirmwareProfile',
    'RX651_CHL': process.env.AWS_SIGNER_PROFILE_NAME || 'Test_ESP32FirmwareProfile',
  };

  // Check for specific firmware types
  for (const [fileType, profile] of Object.entries(profileMappings)) {
    if (objectKey.includes(fileType)) {
      return profile;
    }
  }

  // Default profile for .bin files
  if (objectKey.endsWith('.bin')) {
    return process.env.AWS_SIGNER_PROFILE_NAME || 'Test_ESP32FirmwareProfile';
  }

  return null;
}

async function startSigningJob(request: SigningJobRequest): Promise<void> {
  const signerClient = new SignerClient({ 
    region: process.env.AWS_REGION || 'us-east-1' 
  });

  const command = new StartSigningJobCommand({
    source: {
      s3: {
        bucketName: request.bucketName,
        key: request.key,
        ...(request.version && { version: request.version }),
      },
    },
    destination: {
      s3: {
        bucketName: request.bucketName,
        prefix: request.destinationPrefix,
      },
    },
    profileName: request.profileName,
  });

  try {
    const response = await signerClient.send(command);
    
    logger.info('Signing job started successfully', {
      jobId: response.jobId,
      source: `s3://${request.bucketName}/${request.key}`,
      destination: `s3://${request.bucketName}/${request.destinationPrefix}`,
      profile: request.profileName,
    });

    // TODO: Optionally store job information in database for tracking
    // await storeSigningJobInfo(response.jobId, request);

  } catch (error: any) {
    logger.error('Failed to start signing job', {
      error: error.message,
      source: `s3://${request.bucketName}/${request.key}`,
      profile: request.profileName,
    });
    throw error;
  }
}

// Optional: Store signing job information for tracking
// async function storeSigningJobInfo(jobId: string, request: SigningJobRequest): Promise<void> {
//   // Implementation to store job info in your database
//   // This could be useful for tracking signing job status and history
// }
