import { S3Event, S3EventRecord } from 'aws-lambda';
import { logger } from '../helpers/logger';
import {
  codeSigningFirmware,
} from '../enum/CjcFirmawareEnum';
import { BASE_S3_CODE_SIGNING_PATH } from '../models/interfaces/JobDocumentBuilder';
import { CodeSigningService } from '../services/CodeSigningService';


//This lambda gets invoked when firmware is uploaded to the given bucket and starts code signing

export const handler = async (event: S3Event): Promise<void> => {
  logger.info('Firmware upload notification received', { 
    recordCount: event.Records.length 
  });

  for (const record of event.Records) {
    try {
      await processFirmwareUpload(record);
    } catch (error: any) {
      logger.error('Error processing firmware upload notification', {
        error: error.message,
        record: record.s3,
      });
      // Continue processing other records even if one fails
    }
  }
};

async function processFirmwareUpload(record: S3EventRecord): Promise<void> {
  const codeSigning = new CodeSigningService();
  const { bucket, object } = record.s3;
  const bucketName = bucket.name;
  const objectKey = decodeURIComponent(object.key.replace(/\+/g, ' '));
  const objectVersion = object.versionId;

  logger.info('Processing firmware upload', {
    bucket: bucketName,
    key: objectKey,
    version: objectVersion,
    eventName: record.eventName,
  });

  // Check if this is a firmware file that needs signing
  if (!shouldSignFile(objectKey)) {
    logger.info('File does not require signing, skipping', { key: objectKey });
    return;
  }

  // Determine signing profile based on file type and bucket
  const signingProfile = codeSigning.getSigningProfile(objectKey, bucketName);
  if (!signingProfile) {
    logger.warn('No signing profile found for file', { key: objectKey, bucket: bucketName });
    return;
  }

  // Start AWS Signer job
  await codeSigning.startSigningJob({
    bucketName,
    key: objectKey,
    version: objectVersion,
    profileName: signingProfile,
    destinationPrefix: BASE_S3_CODE_SIGNING_PATH, //----> GatewayDocs/OTA/signed
  });
}

function shouldSignFile(objectKey: string): boolean {
  // Define which files should be signed
  const signingPatterns: RegExp[] = Object.values(codeSigningFirmware).map(
    (pattern) => new RegExp(pattern, 'i'),
  );

  // Skip already signed files
  if (objectKey.includes('/signed/')) {
    return false;
  }

  return signingPatterns.some(pattern => pattern.test(objectKey));
}

