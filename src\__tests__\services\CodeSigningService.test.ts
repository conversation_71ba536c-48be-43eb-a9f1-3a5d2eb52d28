import { CodeSigningService } from '../../services/CodeSigningService';
import {
  SignerClient,
  StartSigningJobCommand,
  DescribeSigningJobCommand,
} from '@aws-sdk/client-signer';
import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { codeSigningProfiles } from '../../enum/CjcFirmawareEnum';

// Mock AWS SDK clients
jest.mock('@aws-sdk/client-signer', () => ({
  SignerClient: jest.fn(),
  StartSigningJobCommand: jest.fn(),
  DescribeSigningJobCommand: jest.fn(),
}));

jest.mock('@aws-sdk/client-s3', () => ({
  S3Client: jest.fn(),
  CopyObjectCommand: jest.fn(),
  DeleteObjectCommand: jest.fn(),
}));

jest.mock('../../enum/CjcFirmawareEnum', () => ({
  codeSigningProfiles: {
    ESP32_firmware: 'TestProfile1',
    ESP32_filesystem: 'TestProfile2',
    RX651_CHL: 'TestProfile3',
  },
}));

const mockSignerSend = jest.fn();
const mockS3Send = jest.fn();

const MockSignerClient = SignerClient as jest.MockedClass<typeof SignerClient>;
const MockS3Client = S3Client as jest.MockedClass<typeof S3Client>;

MockSignerClient.mockImplementation(() => ({
  send: mockSignerSend,
} as any));

MockS3Client.mockImplementation(() => ({
  send: mockS3Send,
} as any));

describe('CodeSigningService', () => {
  let codeSigningService: CodeSigningService;

  beforeEach(() => {
    jest.clearAllMocks();
    mockSignerSend.mockReset();
    mockS3Send.mockReset();
    codeSigningService = new CodeSigningService();
  });

  describe('getSigningProfile', () => {
    it('should return correct profile for ESP32_firmware', () => {
      const result = codeSigningService.getSigningProfile(
        'path/ESP32_firmware.bin',
        'test-bucket',
      );
      expect(result).toBe('TestProfile1');
    });

    it('should return correct profile for ESP32_filesystem', () => {
      const result = codeSigningService.getSigningProfile(
        'path/ESP32_filesystem.bin',
        'test-bucket',
      );
      expect(result).toBe('TestProfile2');
    });

    it('should return correct profile for RX651_CHL', () => {
      const result = codeSigningService.getSigningProfile(
        'path/RX651_CHL.mot',
        'test-bucket',
      );
      expect(result).toBe('TestProfile3');
    });

    it('should return null for unknown file type', () => {
      const result = codeSigningService.getSigningProfile(
        'path/unknown_file.txt',
        'test-bucket',
      );
      expect(result).toBeNull();
    });
  });

  describe('startSigningJob', () => {
    const mockRequest = {
      bucketName: 'test-bucket',
      key: 'path/ESP32_firmware.bin',
      version: 'version123',
      profileName: 'TestProfile1',
      destinationPrefix: 'signed/',
    };

    it('should start signing job successfully', async () => {
      const mockJobId = 'job-123';
      mockSignerSend.mockResolvedValue({ jobId: mockJobId });

      const result = await codeSigningService.startSigningJob(mockRequest);

      expect(result).toBe(mockJobId);
      expect(mockSignerSend).toHaveBeenCalledTimes(1);
      expect(StartSigningJobCommand).toHaveBeenCalledWith({
        source: {
          s3: {
            bucketName: mockRequest.bucketName,
            key: mockRequest.key,
            version: mockRequest.version,
          },
        },
        destination: {
          s3: {
            bucketName: mockRequest.bucketName,
            prefix: mockRequest.destinationPrefix,
          },
        },
        profileName: mockRequest.profileName,
      });
    });

    it('should throw error when version is missing', async () => {
      const requestWithoutVersion = { ...mockRequest, version: undefined };

      await expect(codeSigningService.startSigningJob(requestWithoutVersion))
        .rejects.toThrow('version is required for StartSigningJobCommand');
      
      expect(mockSignerSend).not.toHaveBeenCalled();
    });
  });

  describe('monitorBatchSigningJobs', () => {
    it('should monitor and process successful jobs', async () => {
      // Mock successful job status
      mockSignerSend.mockResolvedValue({
        status: 'Succeeded',
        signedObject: { s3: { key: 'signed/job-1-uuid' } }
      });

      // Mock S3 operations for copy and delete
      mockS3Send.mockResolvedValue({});

      const mockJobRequests = [{
        jobId: 'job-1',
        request: {
          bucketName: 'test-bucket',
          key: 'GatewayDocs/OTA/ESP32/v1.0.0/ESP32_firmware.bin',
          version: 'v1',
          profileName: 'TestProfile1',
          destinationPrefix: 'signed/',
        }
      }];

      await codeSigningService.monitorBatchSigningJobs(mockJobRequests);

      expect(mockSignerSend).toHaveBeenCalledWith(expect.any(DescribeSigningJobCommand));
      expect(mockS3Send).toHaveBeenCalledWith(expect.any(CopyObjectCommand));
      expect(mockS3Send).toHaveBeenCalledWith(expect.any(DeleteObjectCommand));
    });
  });

  describe('delay', () => {
    it('should delay for specified time', async () => {
      const start = Date.now();
      await (codeSigningService as any).delay(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some variance
    });
  });
});
