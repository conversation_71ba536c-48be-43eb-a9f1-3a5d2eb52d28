import { CodeSigningService } from '../../services/CodeSigningService';
import {
  SignerClient,
  StartSigningJobCommand,
  DescribeSigningJobCommand,
} from '@aws-sdk/client-signer';
import {
  S3Client,
  CopyObjectCommand,
  DeleteObjectCommand,
} from '@aws-sdk/client-s3';
import { codeSigningProfiles } from '../../enum/CjcFirmawareEnum';

// Mock AWS SDK clients
jest.mock('@aws-sdk/client-signer');
jest.mock('@aws-sdk/client-s3');
jest.mock('../../enum/CjcFirmawareEnum', () => ({
  codeSigningProfiles: {
    ESP32_firmware: 'TestProfile1',
    ESP32_filesystem: 'TestProfile2',
    RX651_CHL: 'TestProfile3',
  },
}));

const mockSignerClient = {
  send: jest.fn(),
};

const mockS3Client = {
  send: jest.fn(),
};

(SignerClient as jest.Mock).mockImplementation(() => mockSignerClient);
(S3Client as jest.Mock).mockImplementation(() => mockS3Client);

describe('CodeSigningService', () => {
  let codeSigningService: CodeSigningService;

  beforeEach(() => {
    jest.clearAllMocks();
    codeSigningService = new CodeSigningService();
  });

  describe('getSigningProfile', () => {
    it('should return correct profile for ESP32_firmware', () => {
      const result = codeSigningService.getSigningProfile(
        'path/ESP32_firmware.bin',
        'test-bucket',
      );
      expect(result).toBe('TestProfile1');
    });

    it('should return correct profile for ESP32_filesystem', () => {
      const result = codeSigningService.getSigningProfile(
        'path/ESP32_filesystem.bin',
        'test-bucket',
      );
      expect(result).toBe('TestProfile2');
    });

    it('should return correct profile for RX651_CHL', () => {
      const result = codeSigningService.getSigningProfile(
        'path/RX651_CHL.mot',
        'test-bucket',
      );
      expect(result).toBe('TestProfile3');
    });

    it('should return null for unknown file type', () => {
      const result = codeSigningService.getSigningProfile(
        'path/unknown_file.txt',
        'test-bucket',
      );
      expect(result).toBeNull();
    });
  });

  describe('startSigningJob', () => {
    const mockRequest = {
      bucketName: 'test-bucket',
      key: 'path/ESP32_firmware.bin',
      version: 'version123',
      profileName: 'TestProfile1',
      destinationPrefix: 'signed/',
    };

    it('should start signing job successfully', async () => {
      const mockJobId = 'job-123';
      mockSignerClient.send.mockResolvedValue({ jobId: mockJobId });

      const result = await codeSigningService.startSigningJob(mockRequest);

      expect(result).toBe(mockJobId);
      expect(mockSignerClient.send).toHaveBeenCalledWith(
        expect.any(StartSigningJobCommand),
      );
    });

    it('should throw error when version is missing', async () => {
      const requestWithoutVersion = { ...mockRequest, version: undefined };

      await expect(
        codeSigningService.startSigningJob(requestWithoutVersion),
      ).rejects.toThrow('version is required for StartSigningJobCommand');
    });

    it('should handle signing job failure', async () => {
      const error = new Error('Signing failed');
      mockSignerClient.send.mockRejectedValue(error);

      await expect(
        codeSigningService.startSigningJob(mockRequest),
      ).rejects.toThrow('Signing failed');
    });
  });

  describe('monitorBatchSigningJobs', () => {
    const mockJobRequests = [
      {
        jobId: 'job-1',
        request: {
          bucketName: 'test-bucket',
          key: 'path/file1.bin',
          version: 'v1',
          profileName: 'TestProfile1',
          destinationPrefix: 'signed/',
        },
      },
      {
        jobId: 'job-2',
        request: {
          bucketName: 'test-bucket',
          key: 'path/file2.bin',
          version: 'v2',
          profileName: 'TestProfile1',
          destinationPrefix: 'signed/',
        },
      },
    ];

    it('should handle empty job requests', async () => {
      await expect(
        codeSigningService.monitorBatchSigningJobs([]),
      ).resolves.not.toThrow();
    });

    it('should monitor and process successful jobs', async () => {
      // Mock successful job status
      mockSignerClient.send.mockResolvedValue({
        status: 'Succeeded',
        signedObject: { s3: { key: 'signed/job-1-uuid' } },
      });

      // Mock S3 operations
      mockS3Client.send.mockResolvedValue({});

      // Spy on private methods
      const copySignedFileSpy = jest
        .spyOn(codeSigningService as any, 'copySignedFileWithCustomName')
        .mockResolvedValue(undefined);

      await codeSigningService.monitorBatchSigningJobs([mockJobRequests[0]]);

      expect(copySignedFileSpy).toHaveBeenCalledWith(
        'signed/job-1-uuid',
        mockJobRequests[0].request,
      );
    });

    it('should handle failed jobs', async () => {
      // Mock failed job status
      mockSignerClient.send.mockResolvedValue({
        status: 'Failed',
        statusReason: 'Certificate expired',
      });

      await expect(
        codeSigningService.monitorBatchSigningJobs([mockJobRequests[0]]),
      ).resolves.not.toThrow();
    });

    it('should handle job status check errors', async () => {
      mockSignerClient.send.mockRejectedValue(new Error('API Error'));

      await expect(
        codeSigningService.monitorBatchSigningJobs([mockJobRequests[0]]),
      ).resolves.not.toThrow();
    });
  });

  describe('checkSigningJobStatus', () => {
    it('should return job status successfully', async () => {
      const mockResponse = {
        status: 'Succeeded',
        statusReason: 'Job completed',
        signedObject: { s3: { key: 'signed/job-uuid' } },
      };
      mockSignerClient.send.mockResolvedValue(mockResponse);

      const result = await (codeSigningService as any).checkSigningJobStatus(
        'job-123',
      );

      expect(result).toEqual({
        status: 'Succeeded',
        statusReason: 'Job completed',
        signedObjectLocation: 'signed/job-uuid',
      });
    });
  });

  describe('copySignedFileWithCustomName', () => {
    const mockRequest = {
      bucketName: 'test-bucket',
      key: 'GatewayDocs/OTA/ESP32/v1.0.0/ESP32_firmware.bin',
      version: 'v1',
      profileName: 'TestProfile1',
      destinationPrefix: 'signed/',
    };

    it('should copy and delete signed file successfully', async () => {
      mockS3Client.send.mockResolvedValue({});

      await (codeSigningService as any).copySignedFileWithCustomName(
        'signed/job-uuid',
        mockRequest,
      );

      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.any(CopyObjectCommand),
      );
      expect(mockS3Client.send).toHaveBeenCalledWith(
        expect.any(DeleteObjectCommand),
      );
    });

    it('should handle copy operation failure', async () => {
      mockS3Client.send.mockRejectedValue(new Error('S3 Error'));

      await expect(
        (codeSigningService as any).copySignedFileWithCustomName(
          'signed/job-uuid',
          mockRequest,
        ),
      ).rejects.toThrow('S3 Error');
    });
  });

  describe('generateCustomFileName', () => {
    it('should generate correct custom filename', () => {
      const result = (codeSigningService as any).generateCustomFileName(
        'GatewayDocs/OTA/ESP32/v1.0.0/ESP32_firmware.bin',
      );
      expect(result).toBe('ESP32_firmware_signed.bin');
    });

    it('should handle filename without extension', () => {
      const result = (codeSigningService as any).generateCustomFileName(
        'path/to/filename',
      );
      expect(result).toBe('filename_signed.undefined');
    });
  });

  describe('delay', () => {
    it('should delay for specified time', async () => {
      const start = Date.now();
      await (codeSigningService as any).delay(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(90); // Allow some variance
    });
  });
});
