import { resolve } from 'path';
import { Stack as BaseStack, Duration, StackProps } from 'aws-cdk-lib';
import { NameSpacedStage } from '@zephyr/backend-lib-infrastructure';
import { NodejsFunction } from 'aws-cdk-lib/aws-lambda-nodejs';
import { Runtime, Tracing } from 'aws-cdk-lib/aws-lambda';
import { IVpc, Vpc } from 'aws-cdk-lib/aws-ec2';
import * as sqs from 'aws-cdk-lib/aws-sqs';
import { SqsEventSource } from 'aws-cdk-lib/aws-lambda-event-sources';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Configuration } from '../Configuration';
import { RepositoryName } from '../../src/enum/deployment';
import { Helper } from '../Helper';

export class OTAJobStatusSyncStack extends BaseStack {
  private readonly vpc: IVpc;

  private sqSQueue: sqs.Queue;

  private consumerLambda: NodejsFunction;

  constructor(scope: NameSpacedStage, private config: Configuration, id: string, props?: StackProps) {
    super(scope, id, props);

    this.config = config;
    this.vpc = Vpc.fromLookup(this, `${id}-vpc`, {
      vpcId: config.VPC_ID,
    });
    this.createSQSQueue();
    this.consumerLambda = this.createLambdaToUpdateJobStatusOfEdge();
    this.sqSQueue.grantConsumeMessages(this.consumerLambda);
    this.consumerLambda.addEventSource(new SqsEventSource(this.sqSQueue));
    this._grandPermissonLambda();
    this._grantIotPermissionToSQS();
  }

  private _grantIotPermissionToSQS() {
    this.sqSQueue.addToResourcePolicy(
      new iam.PolicyStatement({
        effect: iam.Effect.ALLOW,
        principals: [
          new iam.ArnPrincipal(`arn:${this.partition}:iam::${this.config.CARRIERIO_ACCOUNT_ID}:root`),
          new iam.ArnPrincipal(`arn:${this.partition}:iam::${this.config.AWS_ACCOUNT}:root`),
        ],
        actions: ['SQS:*'],
        sid: 'CrossAccountIoTSendMessageSQS',
        resources: [this.sqSQueue.queueArn],
      }),
    );
  }

  private _grandPermissonLambda() {
    this.consumerLambda.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['secretsmanager:GetSecretValue'],
        effect: iam.Effect.ALLOW,
        resources: ['*'],
      }),
    );
    this.consumerLambda.addToRolePolicy(
      new iam.PolicyStatement({
        actions: ['sts:AssumeRole'],
        effect: iam.Effect.ALLOW,
        resources: [this.config.CARRIERIO_ROLE_ARN],
      }),
    );
  }

  private createSQSQueue() {
    const id = `${this.config.OTA_STATUS_UPDATE_QUEUE}-${this.config.STAGE}`;
    const otaStatusUpdateQueue = new sqs.Queue(this, id, {
      queueName: Helper.convertIdToName(id),
      visibilityTimeout: Duration.minutes(16),
      retentionPeriod: Duration.days(2),
    });

    this.sqSQueue = otaStatusUpdateQueue;
  }

  private createLambdaToUpdateJobStatusOfEdge() {
    return new NodejsFunction(this, 'OTAJobStatusSyncLambda', {
      runtime: Runtime.NODEJS_18_X,
      timeout: Duration.minutes(15),
      memorySize: 1024,
      vpc: this.vpc,
      description: 'A Lambda function to update ota job status of edge',
      functionName: `${RepositoryName.OTAJobStatusSync}-lambda-${this.config.STAGE}`,
      entry: resolve(__dirname, '../../dist/src/lambdas/OTAJobStatusSyncLambda.js'),
      handler: 'handler',
      tracing: Tracing.ACTIVE,
      reservedConcurrentExecutions: 1,
      environment: {
        CARRIERIO_ROLE_ARN: this.config.CARRIERIO_ROLE_ARN,
        STAGE: this.config.STAGE,
        DB_URL: this.config.DB_URL,
        DB_NAME: this.config.DB_NAME,
        SECRET_DB_ARN: this.config.SECRET_DB_ARN,
        SECRET_DB_KEY: this.config.SECRET_DB_KEY,
      },
      deadLetterQueueEnabled: true,
      retryAttempts: 0,
    });
  }
}
